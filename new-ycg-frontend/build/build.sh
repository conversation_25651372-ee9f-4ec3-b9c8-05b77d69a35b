#!/bin/bash
# build.sh - Simple script to build the extension

# Navigate to the build directory
cd "$(dirname "$0")"

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

# Clean and build
echo "Building extension..."
npm run clean || true  # Don't fail if dist doesn't exist yet
npm run build

# Create a zip file for Chrome Web Store submission in the parent directory
echo "Creating youtube-chapter-generator.zip in parent directory..."
cd dist
zip -r ../youtube-chapter-generator.zip *

echo "Build complete! The extension is ready in build/dist/ and the zip file is in the build directory"
