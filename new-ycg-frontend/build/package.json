{"name": "youtube-chapter-generator-extension", "version": "1.0.0", "description": "Chrome extension for generating YouTube video chapters using AI", "private": true, "scripts": {"build": "webpack --mode=production", "clean": "rm -rf dist", "zip": "cd dist && zip -r ../youtube-chapter-generator.zip * && cd ..", "package": "npm run clean && npm run build && npm run zip", "lint": "eslint --config eslint.config.js ../extension"}, "author": "", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "babel-loader": "^9.2.1", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "eslint": "^8.56.0", "mini-css-extract-plugin": "^2.8.0", "style-loader": "^3.3.4", "terser-webpack-plugin": "^5.3.10", "webpack": "^5.90.3", "webpack-cli": "^5.1.4"}}