# Building the YouTube Chapter Generator Extension

This document explains how to build the extension for both development and production environments.

## Directory Structure

- `build/` - Contains build scripts and configuration
- `build/templates/` - Contains environment-specific templates for manifest.json and config.js
- `extension/` - Contains the source code for the extension
- `build/dist/` - Contains the built extension (created during build)
- `build/youtube-chapter-generator-production.zip` - The packaged production extension for Chrome Web Store submission
- `build/youtube-chapter-generator-development.zip` - The packaged development extension for testing

## Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)

## Build Process

### Option 1: Using the build script

The easiest way to build the extension is to use the provided build script with the desired environment:

```bash
# For production build
./build.sh production

# For development build
./build.sh development
```

This script will:
1. Select the appropriate manifest.json and config.js templates based on the environment
2. Install dependencies if needed
3. Clean the dist directory
4. Copy the environment-specific manifest and config files
5. Build the extension using webpack
6. Create a zip file for Chrome Web Store submission or testing

### Option 2: Using npm scripts

You can also use npm scripts to build the extension:

```bash
cd build
# Install dependencies (if needed)
npm install

# Build and package the extension
npm run package
```

This will:
1. Clean the dist directory
2. Build the extension
3. Create a zip file

### Option 3: Manual build steps

If you prefer to run each step manually:

1. Navigate to the build directory:
   ```bash
   cd build
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Clean the dist directory:
   ```bash
   npm run clean
   ```

4. Build the extension:
   ```bash
   npm run build
   ```

5. Create a zip file:
   ```bash
   npm run zip
   ```

The built extension will be in the `build/dist` directory, and the zip file will be in the `build` directory.

## Build Configuration

### Environment-Specific Templates

The build system uses environment-specific templates for:
- `manifest.json` - Controls extension name, version, and OAuth client ID
- `config.js` - Controls API endpoints and other configuration settings

Templates are stored in the `build/templates/` directory with naming convention:
- `manifest.{environment}.json`
- `config.{environment}.js`

### Webpack Configuration

The build process uses webpack to bundle and optimize the extension. The configuration is in `webpack.config.js`.

Key features:
- JavaScript minification
- CSS extraction and optimization
- Asset copying (excluding unnecessary files like .DS_Store)
- Production optimizations

## Linting

The project uses ESLint for code linting. To lint the extension code:

```bash
cd build
npm run lint
```

This will check the JavaScript files in the extension directory for any code style issues or potential problems.

## Troubleshooting

If you encounter any issues during the build process:

1. Make sure you have the correct Node.js and npm versions
2. Try deleting `node_modules` and running `npm install` again
3. Check the webpack configuration for any errors
4. Make sure you have the zip command installed
