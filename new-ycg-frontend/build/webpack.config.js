const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const TerserPlugin = require('terser-webpack-plugin');

// Define extension directory path
const EXTENSION_DIR = path.resolve(__dirname, 'extension');

module.exports = {
  mode: 'production',
  context: EXTENSION_DIR, // Set the context to the extension directory
  entry: {
    // Main entry points
    popup: './js/popup.js',
    ui: './js/ui.js',
    auth: './js/auth/auth_init.js',
    // Use the newer content.js from js directory
    content: './js/content.js',
    background: './background.js',
  },
  output: {
    path: path.resolve(__dirname, 'build/dist'),
    filename: 'js/[name].bundle.js',
  },
  module: {
    rules: [
      {
        test: /\.css$/i,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
        ],
      },
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: require.resolve('babel-loader', { paths: [path.resolve(__dirname, 'build/node_modules')] }),
          options: {
            presets: [require.resolve('@babel/preset-env', { paths: [path.resolve(__dirname, 'build/node_modules')] })],
          },
        },
      },
    ],
  },
  plugins: [
    new CleanWebpackPlugin({
      cleanOnceBeforeBuildPatterns: ['**/*', '!manifest.json', '!js/config.js'],
    }),
    new CopyWebpackPlugin({
      patterns: [
        // Essential extension files
        { from: 'icons', to: 'icons', globOptions: { ignore: ['**/.DS_Store'] } },
        { from: 'css', to: 'css', globOptions: { ignore: ['**/.DS_Store'] } },
        { from: 'popup.html', to: '' },
        
        // Don't copy content.js and background.js as they're bundled by webpack
        
        // Copy individual JS files to maintain current structure
        // But avoid duplicating files that are already bundled
        { from: 'js/state.js', to: 'js/state.js' },
        { from: 'js/api.js', to: 'js/api.js' },
        { from: 'js/video.js', to: 'js/video.js' },
        { from: 'js/auth.js', to: 'js/auth.js' },
        { from: 'js/auth/auth_ui.js', to: 'js/auth/auth_ui.js' },
        { from: 'js/auth/google_oauth.js', to: 'js/auth/google_oauth.js' },
        { from: 'js/auth/token_storage.js', to: 'js/auth/token_storage.js' },
        { from: 'js/popup_feedback.js', to: 'js/popup_feedback.js' },

        // State management files
        { from: 'js/state/store.js', to: 'js/state/store.js' },
        { from: 'js/state/selectors.js', to: 'js/state/selectors.js' },

        // State reducers
        { from: 'js/state/reducers/auth.js', to: 'js/state/reducers/auth.js' },
        { from: 'js/state/reducers/chapters.js', to: 'js/state/reducers/chapters.js' },
        { from: 'js/state/reducers/credits.js', to: 'js/state/reducers/credits.js' },
        { from: 'js/state/reducers/ui.js', to: 'js/state/reducers/ui.js' },
        { from: 'js/state/reducers/video.js', to: 'js/state/reducers/video.js' },

        // State actions
        { from: 'js/state/actions/auth.js', to: 'js/state/actions/auth.js' },
        { from: 'js/state/actions/chapters.js', to: 'js/state/actions/chapters.js' },
        { from: 'js/state/actions/credits.js', to: 'js/state/actions/credits.js' },
        { from: 'js/state/actions/ui.js', to: 'js/state/actions/ui.js' },
        { from: 'js/state/actions/video.js', to: 'js/state/actions/video.js' },

        // State middleware
        { from: 'js/state/middleware/storage.js', to: 'js/state/middleware/storage.js' },

        // Token manager
        { from: 'js/token_manager.js', to: 'js/token_manager.js' },
      ],
    }),
    new MiniCssExtractPlugin({
      filename: 'css/[name].css',
    }),
  ],
  optimization: {
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          format: {
            comments: false,
          },
          compress: {
            drop_console: false, // Keep console logs for now
          },
        },
        extractComments: false,
      }),
    ],
    // Maintain the existing splitChunks configuration
    splitChunks: {
      chunks: 'all',
    },
  },
  resolve: {
    modules: [
      path.resolve(__dirname, 'build/node_modules'),
      'node_modules'
    ]
  },
};
