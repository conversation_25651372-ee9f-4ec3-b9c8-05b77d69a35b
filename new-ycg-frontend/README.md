# YouTube Chapter Generator Frontend

This project contains the static frontend for the YouTube Chapter Generator extension, including Stripe payment redirect pages and Chrome extension assets.

## Deployment
- Deploy the contents of this directory to Vercel as a static site.
- The root should be this folder (containing the `extension/` directory).

## Stripe Redirect URLs
- Payment Success: `/extension/payment-success.html`
- Payment Cancel: `/extension/payment-cancel.html`

## Usage
- Update your backend (API) project to use the new Vercel domain for Stripe redirect URLs.
- Update your Chrome extension to use the new API backend domain for all API calls.

## Project Structure
```
extension/           # Chrome extension source code
  js/                # JavaScript files
    state/           # State management modules
      actions/       # Action creators for each state slice
      reducers/      # Reducers for each state slice
      middleware/    # Middleware functions
      selectors.js   # State selectors
      store.js       # Central store implementation
  css/               # CSS files
  icons/             # Extension icons
  manifest.json      # Extension manifest
  popup.html         # Extension popup
  content.js         # Content script
build/               # Build system files
  templates/         # Environment-specific templates
    manifest.production.json  # Production manifest template
    manifest.development.json # Development manifest template
    config.production.js      # Production config template
    config.development.js     # Development config template
  webpack.config.js  # Webpack configuration
  package.json       # npm package configuration
  BUILD.md           # Build documentation
  eslint.config.js   # ESLint configuration
build.sh             # Main build script (supports environments)
public/              # Static assets for the website
  payment-success.html
  payment-cancel.html
  payment-style.css
  ...
```

## Building the Extension

### Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)

### Development Workflow

The project now supports separate development and production builds with environment-specific configurations.

#### Building for Development

```bash
./build.sh development
```

This creates a development version of the extension with:
- Name: "Test YCG"
- Version: "1.0.1"
- Development Google OAuth client ID
- Debug mode enabled by default

#### Building for Production

```bash
./build.sh production
```

This creates the production version of the extension with:
- Name: "YouTube Chapter Generator"
- Version: "1.0.0"
- Production Google OAuth client ID

### Build Process Details

The build script:
1. Selects the appropriate manifest.json and config.js templates based on the environment
2. Installs dependencies if needed
3. Cleans the dist directory
4. Copies the environment-specific manifest and config files
5. Builds the extension using webpack
6. Creates a zip file (`youtube-chapter-generator-{env}.zip`) in the build directory

For more detailed build instructions, see [build/BUILD.md](build/BUILD.md).

## Chrome Web Store Submission

To submit the extension to the Chrome Web Store:

1. Build the production version of the extension:
   ```bash
   ./build.sh production
   ```
2. Use the generated `youtube-chapter-generator-production.zip` file for submission
3. Follow the Chrome Web Store submission guidelines

For development testing:
1. Build the development version:
   ```bash
   ./build.sh development
   ```
2. Load the unpacked extension from the `build/dist` directory in Chrome's developer mode
3. This version will appear as "Test YCG" in your browser

## Code Quality

### Linting

The project uses ESLint for code linting. To lint the extension code:

```bash
cd build
npm run lint
```

This will check the JavaScript files in the extension directory for any code style issues or potential problems.

## State Management

The extension uses a modular state management system inspired by Redux patterns:

### Key Components

- **Store**: Central state container that holds the application state
- **Actions**: Plain objects describing what happened in the application
- **Reducers**: Pure functions that specify how the state changes in response to actions
- **Selectors**: Functions to extract specific pieces of state
- **Middleware**: Functions that intercept actions before they reach the reducers

### Usage

```javascript
// Dispatch an action
window.YCG_STORE.dispatch("auth", window.YCG_AUTH_ACTIONS.loginStart());

// Get the current state
const state = window.YCG_STORE.getState();

// Subscribe to state changes
const unsubscribe = window.YCG_STORE.subscribe((state) => {
  console.log("State updated:", state);
});

// Use selectors to access state
const isUserAuthenticated = window.YCG_SELECTORS.isAuthenticated(state);
const currentChapters = window.YCG_SELECTORS.getCurrentChapters(state);
```

### State Persistence

The state management system automatically persists relevant parts of the state to `chrome.storage.local`:

- Authentication data
- Credits information
- Video information
- Generated chapters (with 24-hour expiration)

## Credit System

The extension uses a credit system for chapter generation:

- Each user starts with 3 free credits upon signup
- Initial chapter generation for a video costs 1 credit
- The first 2 regenerations for the same video are free (included with the initial credit)
- After the first 3 generations (initial + 2 free regenerations), another credit is used
- The next 2 regenerations are free (included with the second credit)
- Maximum of 6 generations per video (initial + 5 regenerations)

### Credit Balance Updates

- Credit balance is silently refreshed from the backend when the popup is opened (no loading animation)
- Credit balance is refreshed with a loading animation after a successful login
- After a successful purchase, the credit balance is automatically refreshed using a polling mechanism:
  - The payment success page sends a message to the extension when payment is complete
  - The extension starts polling for credit updates (every 3 seconds, up to 10 attempts)
  - Polling stops automatically when credits are updated or maximum attempts are reached
  - A success notification is shown when credits are updated
- The credits badge is always visible in the UI, even when the user has 0 credits
- Credit balance is stored in local storage and persists between browser sessions

### Regeneration Logic

- When a user has exactly 1 credit and uses it for initial generation, they can still make up to 2 free regenerations even with 0 credits
- The backend handles the credit calculation based on the generation count for each video
- The frontend allows regeneration attempts even with 0 credits, letting the backend determine if it should be free

## Notes
- The extension now has a proper build process using webpack.
- Static hosting is still used for the payment redirect pages.
- All build and development tools are kept in the build directory, separate from the extension code.
- The state management system follows Redux patterns for better maintainability.
