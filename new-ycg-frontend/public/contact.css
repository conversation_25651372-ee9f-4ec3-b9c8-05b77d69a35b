/* Contact page specific styles */
.contact-container {
  max-width: 900px;
}

.contact-subtitle {
  color: var(--muted-foreground);
  font-size: 1rem;
  margin-top: 0.5rem;
}

.contact-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
}

/* Contact Info Section */
.contact-info-container {
  background: var(--bg-gradient);
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-info-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.contact-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: white;
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  color: var(--primary-from);
  flex-shrink: 0;
}

.contact-details {
  flex: 1;
}

.contact-details h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.contact-details p {
  color: var(--muted-foreground);
  margin-bottom: 0.5rem;
}

.contact-link {
  display: inline-block;
  color: var(--primary-from);
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 0.75rem;
  transition: color var(--transition-fast);
}

.contact-link:hover {
  color: var(--primary-to);
  text-decoration: underline;
}

.social-links {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  background: white;
  border-radius: var(--radius-full);
  color: var(--foreground);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition);
}

.social-link:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
  color: var(--primary-from);
}

/* Contact Form Section */
.contact-form-container {
  background: white;
  border-radius: var(--radius);
  padding: 1.5rem;
  box-shadow: var(--shadow);
}

.contact-form-container h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--foreground);
  position: relative;
  padding-bottom: 0.75rem;
}

.contact-form-container h2::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  border-radius: 2px;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--foreground);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: var(--radius);
  font-family: inherit;
  font-size: 0.875rem;
  color: var(--foreground);
  background: white;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--muted-foreground);
  opacity: 0.7;
}

.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.75rem;
}

.checkbox-group input {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-from);
}

.checkbox-group label {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

.checkbox-group a {
  color: var(--primary-from);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.checkbox-group a:hover {
  color: var(--primary-to);
  text-decoration: underline;
}

.submit-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  box-shadow: var(--shadow);
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
  align-self: flex-start;
}

.submit-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.submit-btn:hover::before {
  left: 100%;
}

.submit-btn:active {
  transform: translateY(0);
}

/* Success Message */
.form-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
}

.success-icon {
  color: var(--success);
  margin-bottom: 1rem;
}

.form-success h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--foreground);
}

.form-success p {
  color: var(--muted-foreground);
  margin-bottom: 1.5rem;
}

.hidden {
  display: none;
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .contact-content {
    flex-direction: row;
  }

  .contact-info-container {
    width: 40%;
  }

  .contact-form-container {
    width: 60%;
  }
}
