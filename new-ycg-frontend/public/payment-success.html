<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Successful</title>
  <style>
    body { font-family: Inter, sans-serif; background: #f6f8fa; color: #222; display: flex; align-items: center; justify-content: center; height: 100vh; }
    .container { background: #fff; padding: 2rem 3rem; border-radius: 12px; box-shadow: 0 2px 16px rgba(0,0,0,0.06); text-align: center; }
    .success { color: #2ecc40; font-size: 2.5rem; margin-bottom: 1rem; }
    .close { margin-top: 1.5rem; color: #666; font-size: 1rem; }
    .status { margin-top: 1rem; font-size: 0.9rem; color: #666; }
  </style>
</head>
<body>
  <div class="container">
    <div class="success">✅ Payment successful!</div>
    <div>Thank you for your purchase.<br>Your credits will be added to your account shortly.</div>
    <div class="status" id="status">Notifying extension...</div>
    <div class="close">You may now close this window.</div>
  </div>

  <script>
    // Function to notify the extension about the successful payment
    function notifyExtension() {
      try {
        // Send a message to the extension
        window.opener.postMessage({
          type: 'payment_success',
          timestamp: new Date().toISOString()
        }, '*');

        // Update status
        document.getElementById('status').textContent = 'Extension notified! Your credits will be updated shortly.';

        console.log('Payment success message sent to extension');
      } catch (error) {
        console.error('Error notifying extension:', error);
        document.getElementById('status').textContent = 'Unable to notify extension automatically. Please return to the extension.';
      }
    }

    // Notify the extension when the page loads
    window.addEventListener('load', function() {
      // Wait a moment to ensure the extension is ready to receive messages
      setTimeout(notifyExtension, 1000);
    });
  </script>
</body>
</html>
