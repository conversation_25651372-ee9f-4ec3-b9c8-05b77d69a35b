:root {
  /* Primary color palette - vibrant purple gradient */
  --primary-from: #6366f1;
  --primary-to: #8b5cf6;
  --primary-hover: #4f46e5;
  --primary-light: #ede9fe;

  /* Secondary colors */
  --secondary: #64748b;
  --success: #10b981;
  --error: #ef4444;
  --warning: #f59e0b;

  /* Background gradients */
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --card-gradient: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  --menu-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

  /* Text colors */
  --foreground: #0f172a;
  --muted-foreground: #64748b;

  /* UI elements */
  --border: #e2e8f0;
  --radius: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Animation */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
    "Helvetica Neue", sans-serif;
  color: var(--foreground);
  background: var(--bg-gradient);
  line-height: 1.6;
  font-size: 16px;
}

.legal-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.legal-header {
  padding: 2rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-bottom: 1px solid var(--border);
  position: relative;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--secondary);
  text-decoration: none;
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
  transition: color var(--transition-fast);
}

.back-link:hover {
  color: var(--primary-from);
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.logo {
  width: 48px;
  height: 48px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo-container h1 {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.last-updated {
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.legal-content {
  padding: 2rem;
}

.legal-section {
  margin-bottom: 2rem;
}

.legal-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--foreground);
  position: relative;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
}

.legal-section h3 {
  font-size: 1.25rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: var(--foreground);
}

.legal-section p {
  margin-bottom: 1rem;
  color: var(--foreground);
}

.legal-section ul,
.legal-section ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.legal-section li {
  margin-bottom: 0.5rem;
}

.legal-section strong {
  font-weight: 600;
}

.contact-info {
  font-weight: 500;
  color: var(--primary-from);
}

.legal-footer {
  padding: 1.5rem 2rem;
  background: var(--bg-gradient);
  border-top: 1px solid var(--border);
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.footer-link {
  color: var(--secondary);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--primary-from);
}

.footer-link.active {
  color: var(--primary-from);
  font-weight: 500;
}

.divider {
  color: var(--muted-foreground);
  font-size: 0.75rem;
}

.copyright {
  color: var(--muted-foreground);
  font-size: 0.75rem;
}

@media print {
  body {
    background: white;
  }

  .legal-container {
    box-shadow: none;
    margin: 0;
    max-width: 100%;
  }

  .back-link,
  .footer-links {
    display: none;
  }
}
