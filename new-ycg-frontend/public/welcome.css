:root {
  /* Primary color palette - vibrant purple gradient */
  --primary-from: #6366f1;
  --primary-to: #8b5cf6;
  --primary-hover: #4f46e5;
  --primary-light: #ede9fe;

  /* Secondary colors */
  --secondary: #64748b;
  --success: #10b981;
  --error: #ef4444;
  --warning: #f59e0b;

  /* Background gradients */
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --card-gradient: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  --menu-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);

  /* Text colors */
  --foreground: #0f172a;
  --muted-foreground: #64748b;

  /* UI elements */
  --border: #e2e8f0;
  --radius: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Animation */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
    "Helvetica Neue", sans-serif;
  color: var(--foreground);
  background: var(--bg-gradient);
  line-height: 1.5;
  font-size: 14px;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.welcome-container {
  width: 800px;
  max-width: 90%;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.welcome-content {
  padding: 3rem;
  position: relative;
  overflow: hidden;
}

.welcome-content::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
  z-index: -1;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
  position: relative;
}

.welcome-logo {
  width: 100px;
  height: 100px;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
}

.logo-container .welcome-logo {
  animation: none !important;
  transition: none !important;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.logo-container h1 {
  font-size: 2rem;
  font-weight: 700;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.welcome-section {
  margin-bottom: 2rem;
  text-align: center;
}

.welcome-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--foreground);
}

.welcome-section p {
  color: var(--muted-foreground);
  font-size: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.features-section,
.getting-started-section,
.credits-section {
  margin-bottom: 2rem;
}

.features-section h3,
.getting-started-section h3,
.credits-section h3 {
  font-size: 1.25rem;
  margin-bottom: 1rem;
  color: var(--foreground);
  position: relative;
  padding-left: 1rem;
}

.features-section h3::before,
.getting-started-section h3::before,
.credits-section h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.25rem;
  bottom: 0.25rem;
  width: 4px;
  background: linear-gradient(to bottom, var(--primary-from), var(--primary-to));
  border-radius: 2px;
}

.features-list {
  list-style: none;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.features-list li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--card-gradient);
  border-radius: var(--radius);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition), box-shadow var(--transition);
}

.features-list li:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.feature-icon {
  color: var(--primary-from);
  font-size: 1.25rem;
  line-height: 1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.feature-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.feature-text strong {
  font-weight: 600;
  color: var(--foreground);
}

.feature-text span {
  color: var(--muted-foreground);
  font-size: 0.875rem;
}

.steps-list {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.steps-list li {
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.credits-section ul {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.credits-section li {
  margin-bottom: 0.5rem;
  color: var(--foreground);
}

.cta-section {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

.get-started-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  color: white;
  font-weight: 600;
  padding: 0.75rem 2rem;
  border-radius: var(--radius-full);
  text-decoration: none;
  box-shadow: var(--shadow);
  transition: all var(--transition);
  position: relative;
  overflow: hidden;
}

.get-started-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
}

.get-started-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.get-started-btn:hover::before {
  left: 100%;
}

.footer-links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border);
}

.footer-link {
  color: var(--primary-from);
  text-decoration: none;
  font-size: 0.875rem;
  transition: color var(--transition-fast);
}

.footer-link:hover {
  color: var(--primary-to);
  text-decoration: underline;
}

.divider {
  color: var(--muted-foreground);
  font-size: 0.75rem;
}
