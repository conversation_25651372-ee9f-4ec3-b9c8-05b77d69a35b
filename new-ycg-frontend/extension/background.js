/**
 * YouTube Chapter Generator Background Script
 *
 * This script runs in the background and handles extension installation,
 * content script injection, and communication between the popup and content script.
 */

// Log when the background script is loaded
console.log("[Background] YouTube Chapter Generator: Background script loaded");

// Handle extension installation or update
chrome.runtime.onInstalled.addListener((details) => {
  console.log("[Background] Extension installed or updated:", details.reason);

  if (details.reason === "install") {
    console.log("[Background] First installation");

    // Initialize any storage values if needed
    chrome.storage.local.set({
      ycg_first_run: true,
      ycg_install_time: Date.now()
    });
  }
});

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log("[Background] Received message:", request);

  if (request.action === "ensureContentScriptInjected") {
    ensureContentScriptInjected()
      .then(result => {
        console.log("[Background] Content script injection result:", result);
        sendResponse({ success: true, result });
      })
      .catch(error => {
        console.error("[Background] Content script injection error:", error);
        sendResponse({ success: false, error: error.message });
      });

    // Return true to indicate we'll send a response asynchronously
    return true;
  }

  if (request.action === "checkContentScriptStatus") {
    checkContentScriptStatus()
      .then(isActive => {
        console.log("[Background] Content script status:", isActive ? "active" : "inactive");
        sendResponse({ success: true, isActive });
      })
      .catch(error => {
        console.error("[Background] Error checking content script status:", error);
        sendResponse({ success: false, error: error.message });
      });

    // Return true to indicate we'll send a response asynchronously
    return true;
  }
});

/**
 * Check if the content script is active in the current tab
 * @returns {Promise<boolean>} Whether the content script is active
 */
async function checkContentScriptStatus() {
  try {
    // Get the active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    const currentTab = tabs[0];

    if (!currentTab) {
      console.log("[Background] No active tab found");
      return false;
    }

    // Check if the tab is a YouTube video page
    const isYouTubeVideo = currentTab.url &&
                          currentTab.url.includes("youtube.com/watch");

    if (!isYouTubeVideo) {
      console.log("[Background] Not a YouTube video page:", currentTab.url);
      return false;
    }

    // Try to send a ping message to the content script
    return new Promise((resolve) => {
      try {
        chrome.tabs.sendMessage(
          currentTab.id,
          { action: "ping" },
          (response) => {
            // If there's a runtime error, the content script is not active
            if (chrome.runtime.lastError) {
              console.log("[Background] Content script not active:", chrome.runtime.lastError.message);
              resolve(false);
              return;
            }

            // If we got a response, the content script is active
            resolve(!!response);
          }
        );
      } catch (error) {
        console.error("[Background] Error pinging content script:", error);
        resolve(false);
      }
    });
  } catch (error) {
    console.error("[Background] Error checking content script status:", error);
    return false;
  }
}

/**
 * Ensure the content script is injected in the current tab
 * @returns {Promise<string>} Status message
 */
async function ensureContentScriptInjected() {
  try {
    // Get the active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    const currentTab = tabs[0];

    if (!currentTab) {
      return "No active tab found";
    }

    // Check if the tab is a YouTube video page
    const isYouTubeVideo = currentTab.url &&
                          currentTab.url.includes("youtube.com/watch");

    if (!isYouTubeVideo) {
      return "Not a YouTube video page";
    }

    // Check if the content script is already active
    const isActive = await checkContentScriptStatus();

    if (isActive) {
      return "Content script already active";
    }

    // Inject the content script
    await chrome.scripting.executeScript({
      target: { tabId: currentTab.id },
      files: ["js/content.js"]
    });

    // Wait a moment for the script to initialize
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check if the injection was successful
    const isNowActive = await checkContentScriptStatus();

    return isNowActive
      ? "Content script successfully injected"
      : "Content script injection failed";
  } catch (error) {
    console.error("[Background] Error injecting content script:", error);
    return `Error: ${error.message}`;
  }
}
