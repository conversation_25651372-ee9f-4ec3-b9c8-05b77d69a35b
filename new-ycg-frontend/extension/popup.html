<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>YouTube Chapter Generator</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="css/popup.css">
  <!-- All scripts will be loaded at the end of the body -->
</head>
<body>
  <div class="app-container">
    <!-- Welcome Screen -->
    <div id="welcome-container" class="screen full-size">
      <div class="welcome-content full-size">
        <div class="logo-container">
          <img src="icons/icon128.png" alt="Logo" class="logo">
          <h1>YouTube Chapter Generator</h1>
        </div>
        <p class="welcome-message">Create professional chapters for your videos with just one click</p>
        <div id="google-signin-button" class="google-signin-button" tabindex="0" aria-label="Sign in with Google">
          <!-- Google Sign-In button will be rendered here by auth.js -->
        </div>
        <p class="auth-footer">By signing in, you agree to our <a href="#" id="terms-link">Terms of Service</a> and <a href="#" id="privacy-link">Privacy Policy</a></p>
      </div>
    </div>

    <!-- Auth Screen -->
    <div id="auth-container" class="screen hidden">
      <div class="auth-content">
        <h2>Sign In</h2>
        <div id="error-message" class="error-message hidden" role="alert" aria-live="assertive"></div>
        <div id="google-signin-button-auth" class="google-signin-button" tabindex="0" aria-label="Sign in with Google">
          <!-- Google Sign-In button will be rendered here by auth.js -->
        </div>
      </div>
    </div>

    <!-- Main Screen -->
    <div id="main-content" class="screen hidden">
      <!-- Header -->
      <header class="main-header">
        <div class="header-left">
          <img src="icons/icon48.png" alt="Logo" class="header-logo">
          <h2>YouTube Chapter Generator</h2>
        </div>
        <div class="header-right">
          <div class="credits-badge" id="credits-badge" tabindex="0" aria-label="Credits balance">
            <span id="credits-count">3</span>
          </div>
          <button id="settings-btn" class="icon-btn" aria-label="Settings" tabindex="0">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
          </button>
        </div>
      </header>

      <!-- Settings Menu -->
      <div id="user-menu" class="user-menu hidden">
        <div id="user-menu-header" class="user-menu-header">
          <div class="user-details">
            <div id="user-name" class="user-name">User Name</div>
            <div id="user-email" class="user-email"><EMAIL></div>
          </div>
        </div>
        <div class="user-menu-items">
          <a href="#" id="my-account-link" class="menu-item" tabindex="0" aria-label="Buy More Credits">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
              <line x1="1" y1="10" x2="23" y2="10"></line>
            </svg>
            Buy More Credits
          </a>
          <div id="credits-menu" class="credits-menu hidden">
            <div class="credits-option">
              <div class="credits-option-header">
                <h3>10 Credits for $9</h3>
              </div>
              <div class="subscription-toggle">
                <label class="toggle-label" for="subscription-toggle-10">Subscribe</label>
                <div class="toggle-wrapper">
                  <input type="checkbox" id="subscription-toggle-10" class="subscription-checkbox" />
                </div>
              </div>
              <button class="purchase-btn" data-credits="10" data-one-time="price_1RHh4dF7Kryr2ZRbrm1f0zt4" data-subscription="price_1RHh4dF7Kryr2ZRbZwLlf2bT">
                Purchase
              </button>
            </div>
            <div class="credits-option">
              <div class="credits-option-header">
                <h3>50 Credits for $29</h3>
              </div>
              <div class="subscription-toggle">
                <label class="toggle-label" for="subscription-toggle-50">Subscribe</label>
                <div class="toggle-wrapper">
                  <input type="checkbox" id="subscription-toggle-50" class="subscription-checkbox" />
                </div>
              </div>
              <button class="purchase-btn" data-credits="50" data-one-time="price_1RHh4RF7Kryr2ZRbL5HZLqj8" data-subscription="price_1RHh4RF7Kryr2ZRbmHnwUnq4">
                Purchase
              </button>
            </div>
            <div class="back-button-container">
              <button id="back-to-menu-btn" class="back-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="15 18 9 12 15 6"></polyline>
                </svg>
                Back
              </button>
            </div>
          </div>
          <div id="main-menu-items">
            <a href="#" id="feedback-link" class="menu-item" tabindex="0" aria-label="Leave Feedback">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
              Leave Feedback
            </a>
            <a href="#" id="logout-link" class="menu-item" tabindex="0" aria-label="Sign Out">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                <polyline points="16 17 21 12 16 7"></polyline>
                <line x1="21" y1="12" x2="9" y2="12"></line>
              </svg>
              Sign Out
            </a>
          </div>
        </div>
      </div>

      <!-- Main Content Area (will be populated by JS) -->
      <div id="main-content-area" class="main-content-area">
        <!-- This will be populated by JS -->
      </div>
    </div>
  </div>

  <!-- Load scripts in the correct order -->
  <script src="js/config.js"></script>

  <!-- State Management -->
  <script type="module" src="js/state/selectors.js"></script>
  <script type="module" src="js/state/actions/auth.js"></script>
  <script type="module" src="js/state/actions/video.js"></script>
  <script type="module" src="js/state/actions/chapters.js"></script>
  <script type="module" src="js/state/actions/credits.js"></script>
  <script type="module" src="js/state/actions/ui.js"></script>
  <script type="module" src="js/state/store.js"></script>

  <!-- Legacy state.js for backward compatibility -->
  <script src="js/state.js"></script>

  <!-- Core modules -->
  <script src="js/api.js"></script>
  <script src="js/token_manager.js"></script>
  <script src="js/video.js"></script>
  <script src="js/ui.js"></script>
  <script src="js/auth.js"></script>
  <script src="js/popup.js"></script>
</body>
</html>
