"use strict";
(function() {
/**
 * YouTube Chapter Generator API Service Module
 *
 * This module provides a centralized API service for making network requests.
 * It handles authentication, error handling, and request formatting.
 */

// Add global DEBUG flag if not present
if (typeof window.YCG_DEBUG === "undefined") window.YCG_DEBUG = false;

/**
 * API Service class for making network requests
 */
class ApiService {
  constructor() {
    // Initialize API endpoints
    this.API = this.initApiEndpoints();

    this.store = window.YCG_STORE;

    // Request settings
    this.retryCount = 0;
    this.maxRetries = 5; // Increased from 3
    this.retryDelay = 2000; // Increased from 1 second to 2 seconds
    this.timeout = 30000; // 30 seconds default timeout

    // Token refresh settings
    this.isRefreshing = false;
    this.refreshPromise = null;
    this.tokenRefreshInterval = null;
    this.authStateListener = null;
    this.tokenRefreshBuffer = 5 * 60 * 1000; // Refresh token 5 minutes before expiry
    this.lastRefreshAttempt = 0; // Timestamp of last refresh attempt
    this.minRefreshInterval = 60 * 1000; // Minimum 1 minute between refresh attempts

    // Credit balance refresh settings
    this.lastCreditRefresh = 0;
    this.creditRefreshMinInterval = 30 * 1000; // Minimum 30 seconds between credit refreshes

    // Set up visibility change listener to refresh credits when popup becomes visible
    this.setupVisibilityChangeListener();

    // Start token refresh monitoring if we have a token
    // this.setupTokenRefreshMonitoring();
  }

  /**
   * Initialize API endpoints
   * @returns {Object} The API endpoints
   */
  initApiEndpoints() {
    const config = window.YCG_CONFIG;

    if (!config) {
      console.error("[API] YCG_CONFIG is not defined");
      return {};
    }

    return {
      // Base URLs
      BASE_URL: config.API_BASE_URL,
      AUTH_BASE_URL: config.AUTH_BASE_URL,

      // Auth endpoints
      AUTH: {
        LOGIN_GOOGLE: `${config.AUTH_BASE_URL}/login/google`,
        VERIFY_TOKEN: `${config.AUTH_BASE_URL}/verify`,
        REFRESH_TOKEN: `${config.AUTH_BASE_URL}/refresh`,
        USER_INFO: `${config.AUTH_BASE_URL}/user`,
        CONFIG: `${config.AUTH_BASE_URL}/config`,
        LOGOUT: `${config.AUTH_BASE_URL}/logout`,
      },

      // Chapters endpoints
      CHAPTERS: {
        GENERATE: `${config.API_BASE_URL}/v1/chapters/generate`,
      },

      // Credits endpoints
      CREDITS: {
        BALANCE: `${config.API_BASE_URL}/v1/credits/balance`,
      },

      // Payment endpoints
      PAYMENT: {
        PLANS: `${config.API_BASE_URL}/v1/payment/plans`,
        CREATE_SESSION: `${config.API_BASE_URL}/v1/payment/create-checkout-session`,
      },

      // Health check
      HEALTH: {
        PING: `${config.API_BASE_URL}/v1/health`,
      },
    };
  }

  /**
   * Get the authentication token from the store
   * @returns {string|null} The authentication token
   */
  getToken() {
    if (!this.store) {
      console.error("[API] Store is not available");
      return null;
    }

    const state = this.store.getState();
    return state.auth.token;
  }

  /**
   * Make a network request with error handling and authentication
   * @param {string} url - The URL to request
   * @param {Object} options - The fetch options
   * @param {boolean} requiresAuth - Whether the request requires authentication
   * @param {number} timeout - Request timeout in milliseconds (default: 30000)
   * @param {boolean} shouldRefreshToken - Whether to attempt token refresh if needed
   * @returns {Promise<Object>} The response data
   */
  async request(url, options = {}, requiresAuth = false, timeout = 30000, shouldRefreshToken = true) {
    let timeoutId = undefined;
    // Set default options
    const defaultOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    };

    // Merge options
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers,
      },
    };

    // Add authentication token if required
    if (requiresAuth) {
      // Check if token needs refresh before using it
      if (shouldRefreshToken) {
        try {
          await this.checkAndRefreshTokenIfNeeded();
        } catch (refreshError) {
          console.error("[API] Token refresh failed:", refreshError);
          // Continue with the current token, it might still work
        }
      }

      const token = this.getToken();
      if (token) {
        mergedOptions.headers["Authorization"] = `Bearer ${token}`;
      }
    }

    // Debug log for API request
    if (window.YCG_DEBUG) console.log(`[API] ${mergedOptions.method} request to ${url}`);

    try {
      // Use the class timeout if none is provided
      if (!timeout) {
        timeout = this.timeout;
      }

      // Add timeout if not already set
      let controller;
      if (!mergedOptions.signal) {
        controller = new AbortController();
        timeoutId = setTimeout(() => {
          console.log(`[API] Request timeout after ${timeout}ms for ${url}`);
          controller.abort(new DOMException("The operation was aborted due to timeout", "TimeoutError"));
        }, timeout);
        mergedOptions.signal = controller.signal;
      }

      // Make the request
      const response = await fetch(url, mergedOptions);

      // Clear the timeout if we set one
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Handle non-JSON responses
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        if (!response.ok) {
          console.error(`[API] Network error: ${response.status} ${response.statusText}`);
          throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
        }
        return { success: response.ok };
      }

      // Parse JSON response
      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        console.error("[API] Error parsing JSON response:", parseError);
        throw new Error(`Error parsing response: ${parseError.message}`);
      }

      // Handle API errors
      if (!response.ok) {
        const errorMessage = data.error || `HTTP Error: ${response.status}`;
        console.error("[API] API error:", errorMessage, data);
        throw new Error(errorMessage);
      }

      if (window.YCG_DEBUG) console.log("[API] Response data:", data);
      return data;
    } catch (error) {
      // Clear the timeout if we set one
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      // Classify and handle the error
      const errorType = this.classifyError(error);
      // Only log network errors as info, not error
      if (errorType === "network") {
        console.info(`[API] ${errorType} error in ${url}:`, error);
      } else {
        console.error(`[API] ${errorType} error in ${url}:`, error);
      }

      // Handle based on error type
      switch (errorType) {
        case "timeout":
          // For timeout errors, we'll retry with a longer timeout
          if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            if (window.YCG_DEBUG) console.log(`[API] Retrying timed out request (${this.retryCount}/${this.maxRetries})...`);

            // Wait before retrying with exponential backoff
            const delay = this.retryDelay * Math.pow(2, this.retryCount - 1);
            if (window.YCG_DEBUG) console.log(`[API] Waiting ${delay}ms before retry`);
            await new Promise((resolve) => setTimeout(resolve, delay));

            // Retry with a longer timeout
            const longerTimeout = timeout * 1.5; // Increase timeout by 50%
            return this.request(url, options, requiresAuth, longerTimeout, false); // Don't try to refresh token on retry
          }
          // Reset retry count if we're not retrying
          this.retryCount = 0;
          throw new Error(`Request timed out: ${error.message || "The operation took too long to complete"}`);

        case "network":
          // Retry logic for network errors
          if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            if (window.YCG_DEBUG) console.log(`[API] Retrying network request (${this.retryCount}/${this.maxRetries})...`);

            // Wait before retrying with exponential backoff
            const delay = this.retryDelay * Math.pow(2, this.retryCount - 1);
            if (window.YCG_DEBUG) console.log(`[API] Waiting ${delay}ms before retry`);
            await new Promise((resolve) => setTimeout(resolve, delay));

            // Retry the request
            return this.request(url, options, requiresAuth, timeout, false); // Don't try to refresh token on retry
          }
          // Reset retry count if we're not retrying
          this.retryCount = 0;
          throw new Error(`Network error after ${this.maxRetries} retries: ${error.message}`);

        case "auth":
          if (requiresAuth) {
            // If token refresh failed or wasn't attempted, logout
            if (!shouldRefreshToken || url.includes("/refresh")) {
              console.log("[API] Authentication error, logging out");
              // Dispatch logout action
              if (this.store) {
                this.store.dispatch("auth", { type: "LOGOUT" });
                // Save state to storage
                await this.store.saveToStorage();
              }
              throw new Error(`Authentication failed: ${error.message}`);
            } else {
              // Try to refresh the token and retry the request
              try {
                if (window.YCG_DEBUG) console.log("[API] Attempting to refresh token and retry request");
                await this.refreshToken();
                // Retry the original request with the new token
                return this.request(url, options, requiresAuth, timeout, false); // Don't try to refresh token again
              } catch (refreshError) {
                console.error("[API] Token refresh failed:", refreshError);
                // Logout if refresh fails
                if (this.store) {
                  this.store.dispatch("auth", { type: "LOGOUT" });
                  await this.store.saveToStorage();
                }
                throw new Error("Authentication failed: Unable to refresh token");
              }
            }
          }
          throw error;

        default:
          // For other errors, just throw them
          throw error;
      }
    }
  }

  /**
   * Classify an error into specific types for better handling
   * @param {Error} error - The error to classify
   * @returns {string} The error type: 'network', 'auth', 'timeout', or 'unknown'
   */
  classifyError(error) {
    // Check for timeout errors
    if (error.name === "AbortError" ||
        error.name === "TimeoutError" ||
        (error instanceof DOMException && error.name === "AbortError") ||
        error.message.includes("aborted") ||
        error.message.includes("timeout")) {
      return "timeout";
    }

    // Check for network errors
    if (error.message.includes("Failed to fetch") ||
        error.message.includes("Network request failed") ||
        error.message.includes("network error") ||
        error.message.includes("Network Error") ||
        error.message.includes("net::") ||
        error.name === "NetworkError") {
      return "network";
    }

    // Check for authentication errors
    if (error.message.includes("Authentication required") ||
        error.message.includes("Invalid token") ||
        error.message.includes("Token expired") ||
        error.message.includes("Unauthorized") ||
        error.message.includes("Not authenticated") ||
        error.message.includes("JWT") ||
        error.message.includes("token") ||
        error.status === 401 ||
        error.status === 403) {
      return "auth";
    }

    // Default to unknown error type
    return "unknown";
  }

  /**
   * Parse a JWT token and return its payload
   * @param {string} token - The token to parse
   * @returns {Object|null} - The parsed token payload or null if invalid
   */
  parseToken(token) {
    if (!token) return null;

    try {
      // Parse the JWT token
      const base64Url = token.split(".")[1];
      if (!base64Url) {
        console.error("[API] Invalid token format: missing payload segment");
        return null;
      }

      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(atob(base64).split("").map(function(c) {
        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(""));

      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("[API] Error parsing token:", error);
      return null;
    }
  }

  /**
   * Check if a token is expired or about to expire
   * @param {string} token - The JWT token to check
   * @returns {boolean} Whether the token is expired or will expire soon
   */
  isTokenExpiredOrExpiringSoon(token) {
    if (!token) return true;

    try {
      // Use the parseToken method to get the payload
      const payload = this.parseToken(token);
      if (!payload) return true;

      // Check if token has expiration
      if (!payload.exp) return false;

      // Get current time in seconds
      const currentTime = Math.floor(Date.now() / 1000);

      // Check if token is expired or will expire soon
      const expiresIn = payload.exp - currentTime;

      // Use the tokenRefreshBuffer for determining when a token is expiring soon
      // Default to 5 minutes if not set
      const bufferInSeconds = this.tokenRefreshBuffer ? this.tokenRefreshBuffer / 1000 : 300;
      const isExpiringSoon = expiresIn < bufferInSeconds;

      if (isExpiringSoon) {
        const minutesLeft = Math.floor(expiresIn / 60);
        if (window.YCG_DEBUG) console.log(`[API] Token will expire in ${minutesLeft} minutes (${expiresIn} seconds)`);
      }

      return isExpiringSoon;
    } catch (error) {
      console.error("[API] Error checking token expiration:", error);
      // If we can't parse the token, assume it's invalid
      return true;
    }
  }

  /**
   * Verify a token with the server only if local validation is inconclusive or just after login
   * @param {string} token - The token to verify
   * @param {boolean} force - If true, always verify with server (e.g., just after login)
   * @returns {Promise<Object>} The verification result
   */
  async verifyToken(token, force = false) {
    try {
      // First, check if the token is valid by decoding it
      const isLocallyValid = this.parseToken(token) !== null && !this.isTokenExpiredOrExpiringSoon(token);
      if (isLocallyValid && !force) {
        return { valid: true, local: true };
      }
      // If forced, or local validation is inconclusive, verify with server
      const res = await this.request(
        this.API.AUTH.VERIFY_TOKEN,
        {
          method: "POST",
          headers: { "Authorization": `Bearer ${token}` },
        },
        false
      );
      return { valid: true, server: true, res };
    } catch (serverError) {
      // For timeout or network errors, assume token is valid since we already validated it client-side
      if (serverError.message && (serverError.message.includes("timeout") || serverError.message.includes("network"))) {
        return { valid: true, fallback: true };
      } else {
        // For other errors (like auth errors), the token is likely invalid
        throw serverError;
      }
    }
  }

  /**
   * Get the current user's information
   * @returns {Promise<Object>} The user information
   */
  async getUserInfo() {
    try {
      if (window.YCG_DEBUG) console.log("[API] Getting user info...");

      // First, try to get cached user info from store
      const cachedUser = this.getCachedUserInfo();

      // Try to get user info from server with a short timeout
      try {
        const userInfo = await this.request(
          this.API.AUTH.USER_INFO,
          {},
          true, // Requires authentication
          15000, // 15 second timeout
          true  // Try to refresh token if needed
        );
        return userInfo;
      } catch (serverError) {
        // Only log as error if token exists, otherwise info
        const token = this.getToken();
        if (!token) {
          console.info("[API] No token available, user likely logged out. Not an error.");
        } else {
          console.error("[API] Error getting user info from server:", serverError);
        }
        // Fallback to cached user info if possible
        if (cachedUser) {
          return {
            ...cachedUser,
            fallback: true // Indicate this is fallback data
          };
        }
        // If no cached user info, try to extract from token
        if (window.YCG_DEBUG) console.log("[API] No cached user info, trying to extract from token");
        const fallbackUser = this.extractUserInfoFromToken();
        if (fallbackUser) {
          return fallbackUser;
        }
        // If we get here, we couldn't get user info
        throw serverError;
      }
    } catch (error) {
      // Only log as error if token exists, otherwise info
      const token = this.getToken();
      if (!token) {
        console.info("[API] No token available, user likely logged out. Not an error.");
      } else {
        console.error("[API] All attempts to get user info failed:", error);
      }
      throw error;
    }
  }

  /**
   * Get cached user info from the store
   * @returns {Object|null} The cached user info or null if not available
   */
  getCachedUserInfo() {
    if (!this.store) {
      return null;
    }

    const state = this.store.getState();
    // Normalize user object to flat format if legacy nested format is detected
    if (state.auth && state.auth.user) {
      if (state.auth.user.data) {
        // Legacy format detected, normalize and log
        console.warn("[API] Legacy user object detected in cache, normalizing to flat format.", state.auth.user);
        // Merge all keys from data and preserve fallback/success if present
        return {
          ...state.auth.user.data,
          fallback: state.auth.user.fallback,
          success: state.auth.user.success
        };
      }
      // Already flat
      return state.auth.user;
    }
    return null;
  }

  /**
   * Extract user info from the token
   * @returns {Object|null} The user info extracted from the token or null if not available
   */
  extractUserInfoFromToken() {
    try {
      // Try to get user info from token
      const token = this.getToken();
      if (!token) {
        return null;
      }

      // Parse the JWT token to get user info
      const payload = this.parseToken(token);
      if (!payload) {
        return null;
      }

      // Extract basic user info from token (flat structure)
      if (window.YCG_DEBUG) console.log("[API] Using user info from token as fallback");
      return {
        id: payload.sub,
        email: payload.email,
        name: payload.name || payload.email.split("@")[0], // Use email username as fallback
        credits: 95, // Default to 95 credits when offline - this is a temporary fix
        fallback: true, // Indicate this is fallback data
        success: true
      };
    } catch (tokenError) {
      console.error("[API] Error extracting user info from token:", tokenError);
      return null;
    }
  }

  /**
   * Setup token refresh monitoring
   * This sets up a periodic check to refresh the token before it expires
   */
  setupTokenRefreshMonitoring() {
    // Always clear any existing interval first
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
      if (window.YCG_DEBUG) console.log("[API] Cleared existing token refresh interval");
    }
    // Only start if authenticated
    if (!this.store) return;
    const state = this.store.getState();
    if (!state.auth || !state.auth.isAuthenticated || !state.auth.token) {
      if (window.YCG_DEBUG) console.log("[API] Not authenticated, not starting token refresh monitor");
      return;
    }
    // Immediately check if token needs refresh
    this.checkAndRefreshTokenIfNeeded();
    // Set up periodic check
    this.tokenRefreshInterval = setInterval(() => {
      this.checkAndRefreshTokenIfNeeded();
    }, 5 * 60 * 1000); // every 5 minutes
    if (window.YCG_DEBUG) console.log("[API] Started token refresh interval");
    // Listen for auth state changes to stop/start monitor
    if (!this.authStateListener) {
      this.authStateListener = () => {
        const state = this.store.getState();
        const isAuthenticated = state && state.auth && state.auth.isAuthenticated;
        if (!isAuthenticated && this.tokenRefreshInterval) {
          if (window.YCG_DEBUG) console.log("[API] User logged out, stopping token refresh monitoring");
          clearInterval(this.tokenRefreshInterval);
          this.tokenRefreshInterval = null;
        } else if (isAuthenticated && !this.tokenRefreshInterval) {
          if (window.YCG_DEBUG) console.log("[API] User logged in, starting token refresh monitoring");
          this.setupTokenRefreshMonitoring();
        }
      };
      this.store.subscribe(this.authStateListener);
    }
  }

  /**
   * Check if token needs refresh and refresh it if needed
   * @returns {Promise<void>}
   */
  async checkAndRefreshTokenIfNeeded() {
    // const tokenObj = await this._loadTokenObj(); // REMOVED: Read from store instead
    // if (!tokenObj || !tokenObj.access_token) return; // REMOVED
    if (!this.store) return;
    const authState = this.store.getState().auth;
    if (!authState || !authState.token) return;

    // Check if token is expired or expiring soon
    if (this.isTokenExpiredOrExpiringSoon(authState.token)) {
      try {
        if (window.YCG_DEBUG) console.log("[API] Token expiring soon, attempting refresh via checkAndRefreshTokenIfNeeded.");
        await this.refreshToken();
      } catch (e) {
        console.error("[API] Error during proactive token refresh in checkAndRefreshTokenIfNeeded:", e);
        // Optional: dispatch logout if refresh fails critically here, or let next request handle it
      }
    }
  }

  // --- TOKEN STORAGE/HELPERS --- // REMOVED _loadTokenObj, _saveTokenObj, _clearTokenObj
  // async _loadTokenObj() {
  //   if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
  //     const result = await chrome.storage.local.get("ycg_token");
  //     return result.ycg_token;
  //   }
  //   return null;
  // }

  // async _saveTokenObj(tokenObj) {
  //   if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
  //     await chrome.storage.local.set({ ycg_token: tokenObj });
  //   }
  // }

  // async _clearTokenObj() {
  //   if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
  //     await chrome.storage.local.remove("ycg_token");
  //   }
  // }

  // --- TOKEN REFRESH LOGIC ---
  async refreshToken() {
    if (window.YCG_DEBUG) console.log("[API-DEBUG] refreshToken method called");

    if (!this.store) {
      console.error("[API-DEBUG] Store not available for refreshToken.");
      throw new Error("Store not available");
    }

    const authState = this.store.getState().auth;
    const currentRefreshToken = authState.refreshToken;
    const currentUserId = authState.user ? authState.user.id : null;

    if (window.YCG_DEBUG) console.log("[API-DEBUG] Current auth state:", {
      isAuthenticated: authState.isAuthenticated,
      hasToken: !!authState.token,
      hasRefreshToken: !!currentRefreshToken,
      hasUserId: !!currentUserId
    });

    if (!currentRefreshToken || !currentUserId) {
      console.error("[API-DEBUG] Refresh Error: No refresh token or user ID available in store.", {
        hasRefreshToken: !!currentRefreshToken,
        hasUserId: !!currentUserId
      });

      // Try to get a new token via Google Sign-In
      return this.refreshViaGoogleSignIn();
    }

    // Prevent multiple refresh attempts simultaneously
    if (this.isRefreshing) {
      if (window.YCG_DEBUG) console.log("[API-DEBUG] Token refresh already in progress, awaiting existing promise.");
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    if (window.YCG_DEBUG) console.log("[API-DEBUG] Attempting to refresh token...");

    this.refreshPromise = (async () => {
      try {
        // Simple direct API call
        if (window.YCG_DEBUG) console.log("[API-DEBUG] Making direct API call to refresh token");
        if (window.YCG_DEBUG) console.log("[API-DEBUG] Using refresh token:", currentRefreshToken.substring(0, 10) + "...");
        if (window.YCG_DEBUG) console.log("[API-DEBUG] Using user ID:", currentUserId);

        const response = await fetch("https://new-ycg.vercel.app/v1/auth/refresh", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            user_id: currentUserId,
            refresh_token: currentRefreshToken,
          }),
        });

        if (window.YCG_DEBUG) console.log(`[API-DEBUG] Refresh token response status: ${response.status}`);

        const data = await response.json();

        if (window.YCG_DEBUG) console.log(`[API-DEBUG] Refresh token response data:`, data);

        if (!response.ok || !data.success) {
          console.error("[API-DEBUG] Failed to refresh token from backend:", data.error || "Unknown error");

          // If the refresh token is invalid, try to get a new token via Google Sign-In
          if (data.error === "Invalid refresh token") {
            if (window.YCG_DEBUG) console.log("[API-DEBUG] Invalid refresh token, trying Google Sign-In");
            return await this.refreshViaGoogleSignIn();
          }

          // If refresh fails for other reasons, logout
          if (this.store && this.store.dispatch) {
            if (window.YCG_DEBUG) console.log("[API-DEBUG] Dispatching LOGOUT action due to refresh failure");
            this.store.dispatch("auth", { type: "LOGOUT" });
            await this.store.saveToStorage(); // Ensure logout state is persisted
            if (window.YCG_DEBUG) console.log("[API-DEBUG] State saved after logout");
          }
          throw new Error(data.error || "Failed to refresh token");
        }

        const newAccessToken = data.data.access_token;
        const newRefreshToken = data.data.refresh_token;

        if (window.YCG_DEBUG) console.log("[API-DEBUG] Got new tokens:", {
          accessToken: newAccessToken ? newAccessToken.substring(0, 10) + "..." : "missing",
          refreshToken: newRefreshToken ? newRefreshToken.substring(0, 10) + "..." : "missing"
        });

        // Check if we have both tokens
        if (!newAccessToken || !newRefreshToken) {
          console.error("[API-DEBUG] Missing token in response:", {
            hasAccessToken: !!newAccessToken,
            hasRefreshToken: !!newRefreshToken
          });

          // Try to get a new token via Google Sign-In
          return await this.refreshViaGoogleSignIn();
        }

        // Save new tokens to store
        if (this.store && this.store.dispatch) {
          if (window.YCG_DEBUG) console.log("[API-DEBUG] Dispatching UPDATE_TOKENS action");

          // Check if auth actions are available
          if (window.YCG_AUTH_ACTIONS && window.YCG_AUTH_ACTIONS.updateTokens) {
            if (window.YCG_DEBUG) console.log("[API-DEBUG] Using auth actions for token update");
            this.store.dispatch("auth", window.YCG_AUTH_ACTIONS.updateTokens(
              newAccessToken,
              newRefreshToken
            ));
          } else {
            if (window.YCG_DEBUG) console.log("[API-DEBUG] Using direct dispatch for token update");
            this.store.dispatch("auth", {
              type: "UPDATE_TOKENS",
              payload: {
                token: newAccessToken,
                refreshToken: newRefreshToken
              }
            });
          }

          // Check state after dispatch
          const stateAfterDispatch = this.store.getState().auth;
          if (window.YCG_DEBUG) console.log("[API-DEBUG] State after dispatch:", {
            isAuthenticated: stateAfterDispatch.isAuthenticated,
            hasToken: !!stateAfterDispatch.token,
            hasRefreshToken: !!stateAfterDispatch.refreshToken
          });

          // Save to storage immediately to ensure tokens are persisted
          await this.store.saveToStorage();
          if (window.YCG_DEBUG) console.log("[API-DEBUG] State saved to storage after token refresh");
        }

        if (window.YCG_DEBUG) console.log("[API-DEBUG] Token refreshed successfully via refreshToken method.");
        return newAccessToken;
      } catch (error) {
        console.error("[API-DEBUG] Error refreshing token:", error);

        // If all else fails, try Google Sign-In as a last resort
        try {
          return await this.refreshViaGoogleSignIn();
        } catch (googleSignInError) {
          console.error("[API-DEBUG] Google Sign-In refresh failed:", googleSignInError);
          throw error; // Throw the original error
        }
      } finally {
        this.isRefreshing = false;
        this.refreshPromise = null;
        if (window.YCG_DEBUG) console.log("[API-DEBUG] Refresh process completed");
      }
    })();

    return this.refreshPromise;
  }

  /**
   * Refresh tokens via Google Sign-In
   * This is a fallback method when refresh token is invalid or missing
   * @returns {Promise<string>} The new access token
   */
  async refreshViaGoogleSignIn() {
    if (window.YCG_DEBUG) console.log("[API-DEBUG] Attempting to refresh via Google Sign-In");

    try {
      // Try to get a new Google token via silent reauth
      const googleToken = await this.getGoogleTokenSilently();
      if (!googleToken) {
        throw new Error("Failed to get Google token silently");
      }

      if (window.YCG_DEBUG) console.log("[API-DEBUG] Got Google token silently, exchanging for backend tokens");

      // Exchange Google token for backend JWT + user info
      const loginResult = await this.loginWithGoogle(googleToken);
      if (!loginResult || !loginResult.access_token) {
        throw new Error("Failed to exchange Google token for backend tokens");
      }

      // Extract tokens
      const { access_token, refresh_token } = loginResult;

      if (window.YCG_DEBUG) console.log("[API-DEBUG] Successfully exchanged Google token for backend tokens");

      // Update auth state
      if (this.store && this.store.dispatch) {
        this.store.dispatch("auth", {
          type: "UPDATE_TOKENS",
          payload: {
            token: access_token,
            refreshToken: refresh_token
          }
        });

        // Save to storage immediately
        await this.store.saveToStorage();
        if (window.YCG_DEBUG) console.log("[API-DEBUG] State saved to storage after Google Sign-In refresh");
      }

      return access_token;
    } catch (error) {
      console.error("[API-DEBUG] Error refreshing via Google Sign-In:", error);
      throw error;
    }
  }

  /**
   * Get a Google token silently (without user interaction)
   * @returns {Promise<string>} The Google OAuth token
   */
  async getGoogleTokenSilently() {
    if (window.YCG_DEBUG) console.log("[API-DEBUG] Getting Google token silently");

    return new Promise((resolve, reject) => {
      if (!chrome.identity) {
        reject(new Error("chrome.identity not available"));
        return;
      }

      // First clear any cached tokens to ensure we get a fresh one
      chrome.identity.clearAllCachedAuthTokens(() => {
        if (window.YCG_DEBUG) console.log("[API-DEBUG] Cleared cached Google tokens");

        // Get a new token without user interaction
        chrome.identity.getAuthToken({ interactive: false }, (token) => {
          if (chrome.runtime.lastError || !token) {
            console.warn("[API-DEBUG] Silent Google auth failed:", chrome.runtime.lastError);
            reject(new Error("Silent Google auth failed"));
            return;
          }

          if (window.YCG_DEBUG) console.log("[API-DEBUG] Got Google token silently:", token.substring(0, 10) + "...");
          resolve(token);
        });
      });
    });
  }

  // --- LOGOUT LOGIC ---
  async logout() {
    // Always clear token refresh interval and listener
    if (this.tokenRefreshInterval) {
      clearInterval(this.tokenRefreshInterval);
      this.tokenRefreshInterval = null;
      if (window.YCG_DEBUG) console.log("[API] Cleared token refresh interval on logout");
    }
    if (this.authStateListener && this.store) {
      this.store.unsubscribe(this.authStateListener);
      this.authStateListener = null;
      if (window.YCG_DEBUG) console.log("[API] Unsubscribed auth state listener on logout");
    }

    // const tokenObj = await this._loadTokenObj(); // REMOVED
    // if (!tokenObj) return; // REMOVED

    const authState = this.store ? this.store.getState().auth : null;
    const body = {};
    if (authState && authState.user && authState.user.id) {
      body.user_id = authState.user.id;
    }
    if (authState && authState.refreshToken) {
      body.refresh_token = authState.refreshToken;
    }
    // Note: google_token is not typically stored in authState by default,
    // if it needs to be sent, it has to be retrieved from chrome.identity or passed explicitly.
    // For now, we'll assume it's not part of the standard logout call from ApiService.

    try {
      if (Object.keys(body).length > 0) { // Only call backend logout if we have something to send
        await fetch(this.API.AUTH.LOGOUT, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(body),
        });
      }
    } catch (e) {
      // Ignore errors on backend logout call
      console.warn("[API] Error calling backend logout, proceeding with local logout:", e);
    }

    // await this._clearTokenObj(); // REMOVED
    if (this.store && this.store.dispatch) {
      this.store.dispatch("auth", { type: "LOGOUT" });
      // The storage middleware should handle saving the store after LOGOUT
    }
  }

  /**
   * Login with Google
   * @param {string} googleToken - The Google OAuth token
   * @returns {Promise<Object>} The login result
   */
  async loginWithGoogle(googleToken) {
    let timeoutId = undefined;
    // Add retry logic specifically for login
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (window.YCG_DEBUG) console.log(`[API] Login attempt ${attempt}/${maxRetries}`);

        // Use a longer timeout for login requests
        const controller = new AbortController();
        timeoutId = setTimeout(() => {
          if (window.YCG_DEBUG) console.log("[API] Login request timed out after 20 seconds");
          controller.abort(new DOMException("Login request timed out", "TimeoutError"));
        }, 20000); // 20 second timeout

        const result = await this.request(
          this.API.AUTH.LOGIN_GOOGLE,
          {
            method: "POST",
            body: JSON.stringify({
              token: googleToken,
              platform: "chrome_extension",
            }),
            signal: controller.signal,
          },
          false, // Not requiring auth for this request
          20000, // 20 second timeout
          false  // Don't try to refresh token for this request
        );

        clearTimeout(timeoutId);

        // Handle the nested response structure
        if (window.YCG_DEBUG) console.log("[API] Login response structure:", JSON.stringify(result));

        // Check if the response has a nested data structure
        if (result.success && result.data) {
          if (window.YCG_DEBUG) console.log("[API] Found nested data structure in login response");
          // Return the data object which contains the access_token

          // Start token refresh monitoring after successful login
          this.setupTokenRefreshMonitoring();

          return result.data;
        }

        // Start token refresh monitoring after successful login
        this.setupTokenRefreshMonitoring();

        return result;
      } catch (error) {
        console.error(`[API] Login attempt ${attempt} failed:`, error);

        if (attempt === maxRetries) {
          // Last attempt failed, throw the error
          throw error;
        }

        // Wait before retrying with exponential backoff
        const delay = baseDelay * Math.pow(2, attempt - 1);
        if (window.YCG_DEBUG) console.log(`[API] Retrying login in ${delay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  /**
   * Get the user's credit balance
   * This method now uses the refreshCreditBalance method internally
   * @returns {Promise<Object>} The credit balance
   */
  async getCreditBalance() {
    try {
      // Use the refreshCreditBalance method to get the latest balance
      const refreshSuccess = await this.refreshCreditBalance();

      // If refresh was successful, return the balance from the store
      if (refreshSuccess && this.store) {
        const state = this.store.getState();
        return {
          success: true,
          data: {
            balance: state.credits.count
          }
        };
      }

      // If refresh failed, fall back to the original implementation
      return this.request(this.API.CREDITS.BALANCE, {}, true);
    } catch (error) {
      console.info("[API] Error in getCreditBalance:", error);
      // Fall back to the original implementation
      return this.request(this.API.CREDITS.BALANCE, {}, true);
    }
  }

  /**
   * Generate chapters for a video
   * @param {string} videoId - The YouTube video ID
   * @param {string} videoTitle - The YouTube video title
   * @param {boolean} [force] - Whether to force regeneration (bypass cache)
   * @returns {Promise<Object>} The generated chapters
   */
  async generateChapters(videoId, videoTitle, force = false) {
    const body = {
      video_id: videoId,
      video_title: videoTitle,
    };
    if (force) {
      body.force = true;
    }
    return this.request(
      this.API.CHAPTERS.GENERATE,
      {
        method: "POST",
        body: JSON.stringify(body),
      },
      true,
    );
  }

  /**
   * Get available payment plans
   * @returns {Promise<Object>} The payment plans
   */
  async getPaymentPlans() {
    return this.request(this.API.PAYMENT.PLANS);
  }

  /**
   * Create a Stripe checkout session
   * @param {string} priceId - The Stripe Price ID
   * @param {string} mode - 'payment' or 'subscription'
   * @returns {Promise<Object>} The checkout session
   */
  async createCheckoutSession(priceId, mode) {
    try {
      // Use getToken instead of getAccessToken (which does not exist)
      const token = this.getToken();
      if (!token) {
        return { success: false, error: 'Not authenticated' };
      }
      const res = await fetch(`${this.API.BASE_URL}/v1/payment/create-checkout-session`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({ price_id: priceId, mode }),
      });
      const data = await res.json();
      if (!res.ok) return { success: false, error: data.message || "Unknown error" };
      return { success: true, data: data.data };
    } catch (err) {
      return { success: false, error: err.message || err };
    }
  }

  // (DEPRECATED) Create a payment session by plan ID
  // async createPaymentSession(planId) {
  //   return this.request(
  //     this.API.PAYMENT.CREATE_SESSION,
  //     {
  //       method: "POST",
  //       body: JSON.stringify({ plan_id: planId }),
  //     },
  //     true,
  //   );
  // }

  /**
   * Check if the API is available
   * @returns {Promise<boolean>} Whether the API is available
   */
  async ping() {
    try {
      await this.request(this.API.HEALTH.PING);
      return true;
    } catch (error) {
      console.error("[API] Ping failed:", error);
      return false;
    }
  }

  /**
   * Set up visibility change listener to refresh credits when popup becomes visible
   * This ensures credit balance is always up-to-date when the user opens the popup
   */
  setupVisibilityChangeListener() {
    // Only set up the listener if we're in a browser context with document
    if (typeof document === 'undefined') return;

    document.addEventListener('visibilitychange', () => {
      // Only refresh when document becomes visible
      if (document.visibilityState === 'visible') {
        console.log("[API] Popup became visible, refreshing credit balance");
        this.refreshCreditBalance().catch(error => {
          // Only log as info to avoid showing as error in extension
          console.info("[API] Error refreshing credit balance on visibility change:", error);
        });
      }
    });

    // Also refresh on initial load if document is already visible
    if (document.visibilityState === 'visible') {
      // Delay initial refresh to ensure store is fully initialized
      setTimeout(() => {
        console.log("[API] Initial visibility refresh");
        this.refreshCreditBalance().catch(error => {
          console.info("[API] Error in initial credit balance refresh:", error);
        });
      }, 1000);
    }
  }

  /**
   * Refresh the user's credit balance from the backend
   * This is a silent refresh that doesn't show loading animations
   * @returns {Promise<boolean>} Whether the refresh was successful
   */
  async refreshCreditBalance() {
    // Rate limiting - don't refresh too frequently
    const now = Date.now();
    if (now - this.lastCreditRefresh < this.creditRefreshMinInterval) {
      console.log("[API] Credit balance refresh skipped (rate limited)");
      return false;
    }
    this.lastCreditRefresh = now;

    console.log("[API] Starting silent credit balance refresh");

    // Check if store is available
    if (!this.store) {
      console.log("[API] Cannot refresh credit balance: store not available");
      return false;
    }

    // Check if user is logged in
    const state = this.store.getState();
    const isUserLoggedIn = state.auth && state.auth.isAuthenticated;
    if (!isUserLoggedIn) {
      console.log("[API] User not logged in, skipping credit balance refresh");
      return false;
    }

    try {
      // Get auth token
      const token = state.auth.token;
      if (!token) {
        console.log("[API] No auth token available");
        return false;
      }

      // Fetch credit balance
      console.log("[API] Fetching credit balance from API");
      const response = await fetch(this.API.CREDITS.BALANCE, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
          // Add cache busting to ensure fresh data
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache"
        }
      });

      if (!response.ok) {
        console.info("[API] Credit balance API response not OK:", response.status);
        return false;
      }

      const data = await response.json();
      console.log("[API] Credit balance API response:", data);

      if (data && data.success && data.data && data.data.balance !== undefined) {
        const newBalance = data.data.balance;
        const currentBalance = state.credits.count;

        // Only update if balance has changed
        if (newBalance !== currentBalance) {
          console.log("[API] Credit balance updated from", currentBalance, "to", newBalance);

          // Update the store with the new balance
          this.store.dispatch("credits", {
            type: "SET_CREDITS",
            payload: { count: newBalance }
          });

          // Save updated state to storage
          await this.store.saveToStorage();
          console.log("[API] State saved to storage after credit balance update");

          return true;
        } else {
          console.log("[API] Credit balance unchanged, no update needed");
          return true;
        }
      } else {
        console.info("[API] Invalid response format from credit balance API:", data);
        return false;
      }
    } catch (error) {
      console.info("[API] Error fetching credit balance:", error);
      return false;
    }
  }
}

// Create and export the API service
document.addEventListener("DOMContentLoaded", () => {
  // Wait for the DOM to be loaded before creating the API service
  // This ensures that YCG_CONFIG and YCG_STORE are available
  if (window.YCG_CONFIG && window.YCG_STORE) {
    window.YCG_API = new ApiService();
    if (window.YCG_DEBUG) console.log("[API] API service initialized");
  } else {
    console.error("[API] Failed to initialize API service: YCG_CONFIG or YCG_STORE not available");
  }
});

})();
