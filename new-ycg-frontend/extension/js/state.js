"use strict";
(function() {
/**
 * YouTube Chapter Generator State Management Module - Legacy Compatibility Layer
 *
 * This module provides backward compatibility with the original state management system
 * while using the new modular state management system under the hood.
 */

// Add global DEBUG flag if not present
if (typeof window.YCG_DEBUG === "undefined") window.YCG_DEBUG = false;

// Log that we're using the legacy compatibility layer
if (window.YCG_DEBUG) console.log("[STATE] Using legacy compatibility layer for state management");

/**
 * Legacy compatibility wrapper for the new store
 * This class delegates all operations to the new modular store
 */
class Store {
  constructor() {
    // Wait for the new store to be available
    this.checkStoreAvailability();
  }

  /**
   * Check if the new store is available
   */
  checkStoreAvailability() {
    if (window.YCG_STORE) {
      if (window.YCG_DEBUG) console.log("[STATE] New store is available");
    } else {
      if (window.YCG_DEBUG) console.log("[STATE] New store not available yet, waiting...");
      setTimeout(() => this.checkStoreAvailability(), 100);
    }
  }

  /**
   * Get the current state
   * @returns {Object} The current state
   */
  getState() {
    if (window.YCG_STORE) {
      return window.YCG_STORE.getState();
    }
    return {};
  }

  /**
   * Register a reducer function for a specific state slice
   * @param {string} sliceName - The name of the state slice
   * @param {Function} reducer - The reducer function
   */
  registerReducer(sliceName, reducer) {
    if (window.YCG_DEBUG) console.log(`[STATE-LEGACY] Ignoring registerReducer call for ${sliceName} - using new modular reducers`);
    // No-op - we're using the new modular reducers
  }

  /**
   * Dispatch an action to update the state
   * @param {string} sliceName - The name of the state slice to update
   * @param {Object} action - The action object with type and payload
   */
  dispatch(sliceName, action) {
    if (window.YCG_STORE) {
      if (window.YCG_DEBUG) console.log(`[STATE-LEGACY] Dispatching action to ${sliceName} via new store:`, action);
      return window.YCG_STORE.dispatch(sliceName, action);
    } else {
      console.error("[STATE-LEGACY] Cannot dispatch action - new store not available yet");
    }
  }

  /**
   * Subscribe to state changes
   * @param {Function} listener - The listener function
   * @returns {Function} A function to unsubscribe
   */
  subscribe(listener) {
    if (window.YCG_STORE) {
      return window.YCG_STORE.subscribe(listener);
    } else {
      console.error("[STATE-LEGACY] Cannot subscribe - new store not available yet");
      return () => {}; // Return empty unsubscribe function
    }
  }

  /**
   * Notify all listeners of state changes
   */
  notifyListeners() {
    if (window.YCG_STORE) {
      window.YCG_STORE.notifyListeners();
    } else {
      console.error("[STATE-LEGACY] Cannot notify listeners - new store not available yet");
    }
  }

  /**
   * Reset the state to initial values
   */
  reset() {
    if (window.YCG_STORE) {
      window.YCG_STORE.reset();
    } else {
      console.error("[STATE-LEGACY] Cannot reset state - new store not available yet");
    }
  }

  /**
   * Save the state to chrome.storage.local
   * @returns {Promise<boolean>} Whether the save was successful
   */
  async saveToStorage() {
    if (window.YCG_STORE) {
      return window.YCG_STORE.saveToStorage();
    } else {
      console.error("[STATE-LEGACY] Cannot save state - new store not available yet");
      return false;
    }
  }

  /**
   * Load the state from chrome.storage.local
   * @returns {Promise<boolean>} Whether the load was successful
   */
  async loadFromStorage() {
    if (window.YCG_STORE) {
      return window.YCG_STORE.loadFromStorage();
    } else {
      console.error("[STATE-LEGACY] Cannot load state - new store not available yet");
      return false;
    }
  }

  /**
   * Check if a state object has valid format
   * @param {Object} state - The state to validate
   * @returns {boolean} - Whether the state is valid
   */
  isValidState(state) {
    if (window.YCG_STORE) {
      return window.YCG_STORE.isValidState(state);
    } else {
      console.error("[STATE-LEGACY] Cannot validate state - new store not available yet");
      return false;
    }
  }

  /**
   * Check if a token is expired
   * @param {string} token - The JWT token to check
   * @returns {boolean} - Whether the token is expired
   */
  isTokenExpired(token) {
    if (window.YCG_STORE) {
      return window.YCG_STORE.isTokenExpired(token);
    } else {
      console.error("[STATE-LEGACY] Cannot check token expiration - new store not available yet");
      return true; // Assume expired if we can't check
    }
  }
}

// Create a legacy store instance
const store = new Store();

// Export the store to the window object
// This will be overwritten by the new store when it's loaded,
// but we need to provide a placeholder for code that runs before the new store is available
window.YCG_STORE = store;

// Log that the legacy compatibility layer is ready
if (window.YCG_DEBUG) console.log("[STATE] Legacy compatibility layer ready");

})();
