// Handles token save/load/clear from chrome.storage
export async function saveToken(tokenObj) {
  if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
    await chrome.storage.local.set({ ycg_token: tokenObj });
  }
}

export async function loadToken() {
  if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
    const result = await chrome.storage.local.get("ycg_token");
    return result.ycg_token;
  }
  return null;
}

export async function clearToken() {
  if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
    await chrome.storage.local.remove("ycg_token");
  }
}

// Helpers for refresh token
export async function getRefreshToken() {
  const tokenObj = await loadToken();
  return tokenObj && tokenObj.refresh_token;
}

export async function getAccessToken() {
  const tokenObj = await loadToken();
  return tokenObj && tokenObj.access_token;
}

export async function getUserId() {
  const tokenObj = await loadToken();
  return tokenObj && tokenObj.user_id;
}
