/**
 * Configuration for the YouTube Chapter Generator extension
 */
"use strict";
(function() {
  const CONFIG = {
    // API endpoints
    API_BASE_URL: "https://new-ycg.vercel.app",
    AUTH_BASE_URL: "https://new-ycg.vercel.app/v1/auth",

    // Google OAuth client ID
    GOOGLE_CLIENT_ID: "373897257675-i561f2gcpv310b61bptj0ge2bmvdm03m.apps.googleusercontent.com",
  };

  // Export the configuration
  window.YCG_CONFIG = CONFIG;

  // Stripe Price IDs for credit packs
  window.YCG_CONFIG.STRIPE_PRICE_IDS = {
    // 9$ product (10 credits)
    ONE_TIME_10: 'price_1RHh4dF7Kryr2ZRbrm1f0zt4',
    SUBSCRIPTION_10: 'price_1RHh4dF7Kryr2ZRbZwLlf2bT',
    PRODUCT_10: 'prod_SC5FM4lyDMSJ4b',
    // 29$ product (50 credits)
    ONE_TIME_50: 'price_1RHh4RF7Kryr2ZRbL5HZLqj8',
    SUBSCRIPTION_50: 'price_1RHh4RF7Kryr2ZRbmHnwUnq4',
    PRODUCT_50: 'prod_SC5E26Z8v4Pu5C',
  };

  // Stripe publishable key (for frontend Stripe.js)
  window.YCG_CONFIG.STRIPE_PUBLISHABLE_KEY = 'pk_live_awVKH0dAVFMr4STGCaNT7RNS';
})();
