/**
 * Token Manager for handling JWT token operations
 * Provides functionality for token validation, expiration checking, and refresh
 */

// Constants
const TOKEN_REFRESH_THRESHOLD_MINUTES = 10; // Refresh token if it expires in less than 10 minutes

/**
 * Parse a JWT token to get its payload
 * @param {string} token - The JWT token to parse
 * @returns {object|null} The decoded payload or null if invalid
 */
function parseToken(token) {
  try {
    if (!token) return null;

    // JWT tokens are in format: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) return null;

    // Decode the payload (middle part)
    const payload = JSON.parse(atob(parts[1]));
    return payload;
  } catch (error) {
    console.error("[TokenManager] Error parsing token:", error);
    return null;
  }
}

/**
 * Check if a token is expired or will expire soon
 * @param {string} token - The JWT token to check
 * @param {number} thresholdMinutes - Minutes threshold for "expiring soon" (default: TOKEN_REFRESH_THRESHOLD_MINUTES)
 * @returns {boolean} True if token is expired or will expire soon, false otherwise
 */
function isTokenExpiredOrExpiringSoon(token, thresholdMinutes = TOKEN_REFRESH_THRESHOLD_MINUTES) {
  try {
    const payload = parseToken(token);
    if (!payload || !payload.exp) return true;

    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;

    // Convert threshold to milliseconds
    const thresholdMs = thresholdMinutes * 60 * 1000;

    // Token is expired or will expire soon
    return timeUntilExpiration < thresholdMs;
  } catch (error) {
    console.error("[TokenManager] Error checking token expiration:", error);
    return true; // Assume expired on error
  }
}

/**
 * Get the expiration time of a token in human-readable format
 * @param {string} token - The JWT token
 * @returns {string} Human-readable expiration time or "Invalid token" if token is invalid
 */
function getTokenExpirationTime(token) {
  try {
    const payload = parseToken(token);
    if (!payload || !payload.exp) return "Invalid token";

    const expirationTime = new Date(payload.exp * 1000);
    return expirationTime.toLocaleString();
  } catch (error) {
    console.error("[TokenManager] Error getting token expiration time:", error);
    return "Error parsing token";
  }
}

/**
 * Get the time until token expiration in minutes
 * @param {string} token - The JWT token
 * @returns {number} Minutes until expiration or 0 if expired/invalid
 */
function getMinutesUntilExpiration(token) {
  try {
    const payload = parseToken(token);
    if (!payload || !payload.exp) return 0;

    const expirationTime = payload.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;

    // Convert to minutes and round
    return Math.max(0, Math.floor(timeUntilExpiration / (60 * 1000)));
  } catch (error) {
    console.error("[TokenManager] Error calculating minutes until expiration:", error);
    return 0;
  }
}

/**
 * Refresh the access token using the refresh token
 * @param {string} userId - The user ID
 * @param {string} refreshToken - The refresh token
 * @returns {Promise<object>} Object containing new access token and refresh token
 * @throws {Error} If token refresh fails
 */
async function refreshAccessToken(userId, refreshToken) {
  try {
    if (!userId || !refreshToken) {
      throw new Error("User ID and refresh token are required");
    }

    if (window.YCG_DEBUG) console.log("[TokenManager] Refreshing access token");

    // Get the API base URL from the API service or use the default
    const apiBaseUrl = "https://new-ycg.vercel.app/v1/auth";

    if (window.YCG_DEBUG) console.log(`[TokenManager] Using API base URL: ${apiBaseUrl}`);

    // Make the refresh request
    const response = await fetch(`${apiBaseUrl}/refresh`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        user_id: userId,
        refresh_token: refreshToken
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      if (window.YCG_DEBUG) console.log(`[TokenManager] Token refresh failed with status ${response.status}: ${JSON.stringify(errorData)}`);
      throw new Error(`Token refresh failed: ${errorData.message || response.statusText}`);
    }

    const data = await response.json();

    if (window.YCG_DEBUG) console.log(`[TokenManager] Token refresh response: ${JSON.stringify(data)}`);

    if (!data.data || !data.data.access_token) {
      throw new Error("Invalid response from refresh endpoint");
    }

    if (window.YCG_DEBUG) console.log("[TokenManager] Token refreshed successfully");

    return {
      accessToken: data.data.access_token,
      refreshToken: data.data.refresh_token || refreshToken // Use new refresh token if provided
    };
  } catch (error) {
    console.error("[TokenManager] Error refreshing token:", error);
    throw error;
  }
}

/**
 * Setup token refresh monitoring
 * @param {object} store - The Redux-like store
 * @param {number} checkIntervalMinutes - How often to check token expiration (in minutes)
 * @returns {number} Interval ID for the monitoring process
 */
function setupTokenRefreshMonitoring(store, checkIntervalMinutes = 5) {
  if (!store) {
    console.error("[TokenManager] Store is required for token monitoring");
    return null;
  }

  // Clear any existing interval
  if (window.tokenRefreshInterval) {
    clearInterval(window.tokenRefreshInterval);
  }

  if (window.YCG_DEBUG) console.log("[TokenManager] Setting up token refresh monitoring");

  // Set up the interval to check token expiration
  const intervalId = setInterval(async () => {
    try {
      const state = store.getState();

      // Skip if not authenticated
      if (!state.auth || !state.auth.isAuthenticated) {
        return;
      }

      const token = state.auth.token;
      const userId = state.auth.user && state.auth.user.id;
      const refreshToken = state.auth.refreshToken;

      // Skip if no token or refresh token
      if (!token || !userId || !refreshToken) {
        return;
      }

      // Check if token is expired or will expire soon
      if (isTokenExpiredOrExpiringSoon(token)) {
        if (window.YCG_DEBUG) {
          console.log("[TokenManager] Token is expired or will expire soon, refreshing");
          console.log(`[TokenManager] Token expires at: ${getTokenExpirationTime(token)}`);
          console.log(`[TokenManager] Minutes until expiration: ${getMinutesUntilExpiration(token)}`);
        }

        try {
          // Refresh the token
          const { accessToken, refreshToken: newRefreshToken } = await refreshAccessToken(userId, refreshToken);

          // Update the store with the new tokens
          store.dispatch("auth", {
            type: "UPDATE_TOKENS",
            payload: {
              token: accessToken,
              refreshToken: newRefreshToken
            }
          });

          // Save the updated state to storage
          await store.saveToStorage();

          if (window.YCG_DEBUG) console.log("[TokenManager] Token refreshed and saved to storage");
        } catch (refreshError) {
          console.error("[TokenManager] Failed to refresh token:", refreshError);

          // If refresh fails, we might need to log the user out
          // But we'll let them continue using the app until the token actually expires
          if (window.YCG_DEBUG) console.log("[TokenManager] Will retry on next interval");
        }
      } else if (window.YCG_DEBUG) {
        console.log("[TokenManager] Token is still valid");
        console.log(`[TokenManager] Token expires at: ${getTokenExpirationTime(token)}`);
        console.log(`[TokenManager] Minutes until expiration: ${getMinutesUntilExpiration(token)}`);
      }
    } catch (error) {
      console.error("[TokenManager] Error in token refresh monitoring:", error);
    }
  }, checkIntervalMinutes * 60 * 1000);

  // Store the interval ID globally so it can be cleared later
  window.tokenRefreshInterval = intervalId;

  return intervalId;
}

/**
 * Stop token refresh monitoring
 */
function stopTokenRefreshMonitoring() {
  if (window.tokenRefreshInterval) {
    clearInterval(window.tokenRefreshInterval);
    window.tokenRefreshInterval = null;
    if (window.YCG_DEBUG) console.log("[TokenManager] Token refresh monitoring stopped");
  }
}

// Export the functions
window.YCG_TOKEN_MANAGER = {
  parseToken,
  isTokenExpiredOrExpiringSoon,
  getTokenExpirationTime,
  getMinutesUntilExpiration,
  refreshAccessToken,
  setupTokenRefreshMonitoring,
  stopTokenRefreshMonitoring
};
