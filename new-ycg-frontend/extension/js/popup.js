/**
 * YouTube Chapter Generator Popup Script
 *
 * This is the main entry point for the popup UI.
 */

"use strict";

(function() {
  // Initialize when DOM is loaded
  document.addEventListener("DOMContentLoaded", async () => {
    // Load debug flag from storage
    if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
      const result = await chrome.storage.local.get("ycg_debug");
      window.YCG_DEBUG = result.ycg_debug === true;
    } else {
      window.YCG_DEBUG = false;
    }

    // Add keyboard shortcut to toggle debug mode
    document.addEventListener("keydown", async (event) => {
      // Ctrl+Shift+D to toggle debug mode
      if (event.ctrlKey && event.shiftKey && event.key === "D") {
        window.YCG_DEBUG = !window.YCG_DEBUG;
        console.log("Debug mode " + (window.YCG_DEBUG ? "enabled" : "disabled"));

        // Save debug setting to storage
        if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
          await chrome.storage.local.set({ ycg_debug: window.YCG_DEBUG });
        }
      }
    });

    console.log = ((origLog) => function(...args) { if (window.YCG_DEBUG) origLog.apply(console, args); })(console.log);
    console.info = ((origInfo) => function(...args) { if (window.YCG_DEBUG) origInfo.apply(console, args); })(console.info);
    console.debug = ((origDebug) => function(...args) { if (window.YCG_DEBUG) origDebug.apply(console, args); })(console.debug);

    // Enable all console logs if debug is enabled
    if (window.YCG_DEBUG) {
      console.log("[POPUP-DEBUG] Debug mode is enabled");
    }

    console.log("[POPUP-DEBUG] DOM content loaded, waiting for services to initialize");

    // Log available services
    const checkServices = () => {
      console.log("[POPUP-DEBUG] Available services:", {
        store: !!window.YCG_STORE,
        api: !!window.YCG_API,
        video: !!window.YCG_VIDEO,
        ui: !!window.YCG_UI,
      });

      // We only require the store and UI to be available
      // API and video services are optional and can be handled gracefully if missing
      return window.YCG_STORE && window.YCG_UI;
    };

    // Try to initialize with increasing delays
    const tryInit = (attempt = 1, maxAttempts = 5) => {
      console.log(`[POPUP-DEBUG] Initialization attempt ${attempt}/${maxAttempts}`);

      if (checkServices()) {
        console.log("[POPUP-DEBUG] All services available, initializing popup");
        init();
        return;
      }

      if (attempt < maxAttempts) {
        const delay = 100 * Math.pow(2, attempt - 1); // Exponential backoff
        console.log(`[POPUP-DEBUG] Services not ready, retrying in ${delay}ms`);
        setTimeout(() => tryInit(attempt + 1, maxAttempts), delay);
      } else {
        console.error("[POPUP-DEBUG] Failed to initialize after multiple attempts");
        // Try to initialize with whatever services are available
        if (window.YCG_STORE) {
          console.log("[POPUP-DEBUG] Attempting initialization with partial services");
          init();
        }
      }
    };

    // Start initialization attempts
    tryInit();
  });

  /**
   * Refresh the user's credit balance from the backend
   * This function is called only in specific scenarios:
   * 1. After a successful purchase
   * 2. When the user logs in
   * 3. When explicitly requested (e.g., via a refresh button)
   */
  async function refreshCreditBalance(options = {}) {
    const { showLoadingAnimation = false, forceRefresh = false } = options;

    // Use console.log for normal operations, console.warn only for warnings
    console.log("[CREDIT-REFRESH] Starting credit balance refresh");

    const store = window.YCG_STORE;
    const api = window.YCG_API;

    // Only proceed if we have both store and API services
    if (!store || !api) {
      console.log("[CREDIT-REFRESH] Cannot refresh credit balance: store or API service not available");
      return false;
    }

    // Check if user is logged in
    const isUserLoggedIn = store.getState().auth && store.getState().auth.isAuthenticated;
    if (!isUserLoggedIn) {
      console.log("[CREDIT-REFRESH] User not logged in, skipping credit balance refresh");
      return false;
    }

    console.log("[CREDIT-REFRESH] User is logged in, refreshing credit balance from backend");

    // Only show loading animation if explicitly requested
    if (showLoadingAnimation) {
      store.dispatch("credits", { type: "LOADING_CREDITS" });
    }

    try {
      // Fetch the latest credit balance
      console.log("[CREDIT-REFRESH] Calling API getCreditBalance()");

      // Direct fetch implementation as a fallback
      const token = store.getState().auth.token;
      if (!token) {
        console.log("[CREDIT-REFRESH] No auth token available");
        return false;
      }

      // Use direct fetch instead of api.getCreditBalance() to bypass any potential issues
      const apiUrl = api.API.CREDITS.BALANCE;
      console.log("[CREDIT-REFRESH] Fetching from URL:", apiUrl);

      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json"
        }
      });

      if (!response.ok) {
        console.log("[CREDIT-REFRESH] API response not OK:", response.status);
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      console.log("[CREDIT-REFRESH] API response data:", data);

      if (data && data.success && data.data && data.data.balance !== undefined) {
        const newBalance = data.data.balance;
        const currentBalance = store.getState().credits.count;
        console.log("[CREDIT-REFRESH] Credit balance updated from", currentBalance, "to", newBalance);

        // Only update if balance has changed or force refresh is requested
        if (newBalance !== currentBalance || forceRefresh) {
          // Update the store with the new balance
          store.dispatch("credits", {
            type: "SET_CREDITS",
            payload: { count: newBalance }
          });

          // Save updated state to storage
          console.log("[CREDIT-REFRESH] Saving state to storage");
          await store.saveToStorage();
          console.log("[CREDIT-REFRESH] State saved to storage");

          // Show notification if balance has increased
          if (newBalance > currentBalance && window.YCG_UI) {
            window.YCG_UI.showNotification(`Credits updated: ${newBalance} credits available`, "success");
          }
        } else {
          console.log("[CREDIT-REFRESH] Credit balance unchanged, skipping update");
        }

        return true; // Return success status
      } else {
        console.log("[CREDIT-REFRESH] Invalid response format from credit balance API:", data);
        if (showLoadingAnimation) {
          store.dispatch("credits", {
            type: "CREDITS_ERROR",
            payload: { error: "Failed to fetch credit balance" }
          });
        }
        return false;
      }
    } catch (error) {
      console.log("[CREDIT-REFRESH] Error fetching credit balance:", error);
      if (showLoadingAnimation) {
        store.dispatch("credits", {
          type: "CREDITS_ERROR",
          payload: { error: error.message || "Unknown error" }
        });
      }
      return false;
    }
  }

  /**
   * Initialize the popup
   */
  async function init() {
    console.log("[POPUP-DEBUG] Initializing popup...");

    // Get references to the store, API, and video service
    const store = window.YCG_STORE;
    const api = window.YCG_API;
    const video = window.YCG_VIDEO;
    const ui = window.YCG_UI;

    console.log("[POPUP-DEBUG] Services for initialization:", {
      store: !!store,
      api: !!api,
      video: !!video,
      ui: !!ui
    });

    // Check if user is logged in (for logging purposes)
    const isUserLoggedIn = store && store.getState().auth && store.getState().auth.isAuthenticated;
    console.log("[POPUP-DEBUG] User login status:", isUserLoggedIn ? "logged in" : "not logged in");

    // Make refreshCreditBalance available globally for debugging and future use
    window.YCG_REFRESH_CREDITS = refreshCreditBalance;

    // Also expose the API's refreshCreditBalance method globally
    // This allows for silent credit balance refreshes from other parts of the code
    if (api && typeof api.refreshCreditBalance === 'function') {
      window.YCG_API_REFRESH_CREDITS = api.refreshCreditBalance.bind(api);
      console.log("[POPUP-INIT] API refreshCreditBalance method exposed globally");
    }

    // Note: We don't need to manually refresh credits here anymore
    // The API service will automatically refresh credits when the popup becomes visible
    // via the visibilitychange event listener

    // Always initialize video service if available
    // The video service will internally decide whether to check for videos based on login state
    if (video) {
      console.log("[POPUP-DEBUG] Initializing video service");

      // Check if we need to clear chapters for a different video
      if (store) {
        const state = store.getState();
        const currentVideoId = state.video && state.video.id;
        const hasChapters = state.chapters &&
                           state.chapters.versions &&
                           state.chapters.versions.length > 0;

        // If we have chapters but no current video ID, we need to check if we're on a new video
        if (hasChapters && !currentVideoId) {
          console.log("[POPUP-DEBUG] We have chapters but no current video ID. Will check if we're on a new video.");
        }
      }

      // Initialize video service to get current video information
      video.init();
    } else {
      console.log("[POPUP-DEBUG] Video service not available");
    }

    // We already refreshed credits above, no need to do it again
    // Just log the status for debugging
    // Note: This is expected behavior when user is not logged in or services are not available
    // It's not an error, just an informational message
    if (!(api && store && isUserLoggedIn)) {
      const conditions = {
        api: !!api,
        store: !!store,
        isUserLoggedIn: isUserLoggedIn
      };
      console.log(`[POPUP-INIT] Not refreshing credits - conditions not met: ${JSON.stringify(conditions)}`);
    }

    let didPing = false;
    // Check API availability only if API service is available
    if (api) {
      try {
        // Only ping API once on popup init
        if (!didPing) {
          console.log("[POPUP-DEBUG] Checking API availability");
          const isApiAvailable = await api.ping().catch(error => {
            console.log("[POPUP-DEBUG] API ping failed:", error);
            return false;
          });
          didPing = true;
          console.log("[POPUP-DEBUG] API availability check result:", isApiAvailable);

          if (!isApiAvailable) {
            console.log("[POPUP-DEBUG] API is not available");
            if (ui) {
              // Only show notification if user is logged in, as API is not needed for login screen
              if (isUserLoggedIn) {
                ui.showNotification("Server is not available. Some features may be limited.", "warning");
              }
            }
          }
        }
      } catch (error) {
        console.log("[POPUP-DEBUG] Error checking API availability:", error);
      }
    } else {
      console.log("[POPUP-DEBUG] API service not available, continuing with limited functionality");
    }

    // Set up event listeners for popup-specific functionality
    console.log("[POPUP-DEBUG] Setting up popup event listeners");
    setupPopupEventListeners();

    // Add store listener to detect auth state changes
    // This listener is used to refresh credits when the user logs in
    if (store) {
      console.log("[POPUP-INIT] Adding store listener for auth state changes");
      let previousAuthState = store.getState().auth?.isAuthenticated;

      store.subscribe(() => {
        const currentState = store.getState();
        const currentAuthState = currentState.auth?.isAuthenticated;

        // If auth state changed from not authenticated to authenticated
        // This is a normal part of the login flow and triggers a credit balance refresh
        if (currentAuthState && !previousAuthState) {
          console.log("[POPUP-STORE] Auth state changed to authenticated, refreshing credits");
          // Refresh credit balance when user becomes authenticated
          if (typeof refreshCreditBalance === "function") {
            refreshCreditBalance().catch(err => {
              console.log("[POPUP-STORE] Error refreshing credits after auth change:", err);
            });
          }
        }

        // Update previous state for next comparison
        previousAuthState = currentAuthState;
      });
    }

    // Force UI update if UI service is available
    if (ui && store) {
      console.log("[POPUP-DEBUG] Forcing UI update");
      ui.updateUI(store.getState());
    }

    console.log("[POPUP-DEBUG] Initialization complete");

    // Add listener for popup close event
    window.addEventListener("beforeunload", async () => {
      console.log("[POPUP-DEBUG] Popup is closing");

      // Ensure state is saved to storage when popup closes
      if (store) {
        console.log("[POPUP-DEBUG] Saving state to storage before closing");
        await store.saveToStorage();
      }
    });
  }

  /**
   * Set up popup-specific event listeners
   */
  function setupPopupEventListeners() {
    // Terms and privacy links
    const termsLink = document.getElementById("terms-link");
    const privacyLink = document.getElementById("privacy-link");
    const myAccountLink = document.getElementById("my-account-link");
    const feedbackLink = document.getElementById("feedback-link");
    const creditsMenu = document.getElementById("credits-menu");
    const mainMenuItems = document.getElementById("main-menu-items");
    const backToMenuBtn = document.getElementById("back-to-menu-btn");
    const purchaseBtns = document.querySelectorAll(".purchase-btn");
    const subscriptionToggles = document.querySelectorAll(".subscription-checkbox");

    if (termsLink) {
      termsLink.addEventListener("click", (event) => {
        event.preventDefault();
        chrome.tabs.create({ url: "https://ycg-frontend.vercel.app/terms.html" });
      });
    }

    if (privacyLink) {
      privacyLink.addEventListener("click", (event) => {
        event.preventDefault();
        chrome.tabs.create({ url: "https://ycg-frontend.vercel.app/privacy.html" });
      });
    }

    if (myAccountLink) {
      myAccountLink.addEventListener("click", (event) => {
        event.preventDefault();
        // Show credits menu and hide main menu items
        if (creditsMenu && mainMenuItems) {
          creditsMenu.classList.remove("hidden");
          mainMenuItems.classList.add("hidden");
          console.log("[BUY-CREDITS] Showing credits menu");
        }
      });
    }

    if (backToMenuBtn) {
      backToMenuBtn.addEventListener("click", () => {
        // Hide credits menu and show main menu items
        if (creditsMenu && mainMenuItems) {
          creditsMenu.classList.add("hidden");
          mainMenuItems.classList.remove("hidden");
          console.log("[BUY-CREDITS] Showing main menu");
        }
      });
    }

    if (feedbackLink) {
      feedbackLink.addEventListener("click", async (event) => {
        event.preventDefault();
        // Lazy load feedback module only when feedback is clicked
        const feedbackModule = await import("./popup_feedback.js");
        feedbackModule.openFeedbackForm();
      });
    }

    // Subscription toggles
    if (subscriptionToggles) {
      subscriptionToggles.forEach((toggle) => {
        const toggleStatus = toggle.nextElementSibling;
        toggle.addEventListener("change", () => {
          if (toggleStatus) {
            toggleStatus.textContent = toggle.checked ? "Subscription" : "One-time";
          }
          // Update the associated purchase button text
          const parentOption = toggle.closest(".credits-option");
          if (parentOption) {
            const purchaseBtn = parentOption.querySelector(".purchase-btn");
            if (purchaseBtn) {
              purchaseBtn.textContent = `Purchase`;
            }
          }
        });
      });
    }

    // Purchase button logic
    if (purchaseBtns && window.YCG_API) {
      purchaseBtns.forEach((btn) => {
        btn.addEventListener("click", async (event) => {
          event.preventDefault();
          // Find the parent option and get the toggle state
          const parentOption = btn.closest(".credits-option");
          const toggle = parentOption.querySelector(".subscription-checkbox");
          const isSubscription = toggle && toggle.checked;
          // Select priceId based on toggle
          const priceId = btn.getAttribute(isSubscription ? "data-subscription" : "data-one-time");
          const mode = isSubscription ? "subscription" : "payment";
          console.log(`[BUY-CREDITS] Purchase button clicked. priceId=${priceId}, mode=${mode}`);
          if (!priceId || !mode) {
            console.log("[BUY-CREDITS] Missing priceId or mode.", { priceId, mode });
            return;
          }
          btn.disabled = true;
          btn.textContent = "Redirecting...";
          try {
            const result = await window.YCG_API.createCheckoutSession(priceId, mode);
            console.log("[BUY-CREDITS] createCheckoutSession result:", result);
            if (result && result.success && result.data && result.data.url) {
              // Open the checkout URL in a new tab
              window.open(result.data.url, "_blank");

              // Set up a message listener to wait for the success page notification
              console.log("[BUY-CREDITS] Setting up message listener for payment success");

              // Function to handle credit refresh after successful payment
              const handlePaymentSuccess = async () => {
                console.log("[BUY-CREDITS] Payment success detected, starting credit refresh");

                // Store the current credit balance to detect changes
                const initialCredits = window.YCG_STORE ? window.YCG_STORE.getState().credits.count : 0;
                console.log("[BUY-CREDITS] Initial credit balance:", initialCredits);

                // Set up polling to check for credit updates
                let pollCount = 0;
                const maxPolls = 10; // Try up to 10 times
                const pollInterval = 3000; // Every 3 seconds

                const pollForCreditUpdate = async () => {
                  if (pollCount >= maxPolls) {
                    console.log("[BUY-CREDITS] Reached maximum poll attempts, stopping");
                    return;
                  }

                  pollCount++;
                  console.log(`[BUY-CREDITS] Polling for credit update (attempt ${pollCount}/${maxPolls})`);

                  try {
                    // Try to use the API's silent refresh method first
                    if (window.YCG_API_REFRESH_CREDITS && typeof window.YCG_API_REFRESH_CREDITS === 'function') {
                      try {
                        console.log(`[BUY-CREDITS] Using API silent refresh method (attempt ${pollCount}/${maxPolls})`);
                        await window.YCG_API_REFRESH_CREDITS();
                      } catch (apiRefreshError) {
                        console.log("[BUY-CREDITS] API silent refresh failed, falling back to legacy method:", apiRefreshError);

                        // Fall back to the legacy refresh method
                        if (window.YCG_REFRESH_CREDITS && typeof window.YCG_REFRESH_CREDITS === 'function') {
                          // First poll should show loading animation
                          const showLoading = pollCount === 1;
                          console.log(`[BUY-CREDITS] Falling back to legacy refresh (showLoading=${showLoading})`);

                          await window.YCG_REFRESH_CREDITS({
                            showLoadingAnimation: showLoading,
                            forceRefresh: true
                          });
                        }
                      }
                    } else if (window.YCG_REFRESH_CREDITS && typeof window.YCG_REFRESH_CREDITS === 'function') {
                      // If API refresh is not available, use the legacy method
                      const showLoading = pollCount === 1;
                      console.log(`[BUY-CREDITS] Using legacy refresh method (showLoading=${showLoading})`);

                      await window.YCG_REFRESH_CREDITS({
                        showLoadingAnimation: showLoading,
                        forceRefresh: true
                      });
                    } else {
                      console.log("[BUY-CREDITS] No refresh function available");
                    }

                    // Check if credits have been updated
                    const currentCredits = window.YCG_STORE ? window.YCG_STORE.getState().credits.count : 0;
                    console.log("[BUY-CREDITS] Current credit balance:", currentCredits);

                    if (currentCredits > initialCredits) {
                      console.log("[BUY-CREDITS] Credits updated successfully, stopping polling");
                      // Show a success notification
                      if (window.YCG_UI) {
                        window.YCG_UI.showNotification(`Purchase successful! ${currentCredits} credits available.`, "success");
                      }
                      return; // Stop polling if credits have been updated
                    }

                    // Continue polling if credits haven't been updated yet
                    if (pollCount < maxPolls) {
                      setTimeout(pollForCreditUpdate, pollInterval);
                    }
                  } catch (error) {
                    console.log("[BUY-CREDITS] Error refreshing credits during polling:", error);
                    // Continue polling despite errors
                    if (pollCount < maxPolls) {
                      setTimeout(pollForCreditUpdate, pollInterval);
                    }
                  }
                };

                // Start polling immediately
                pollForCreditUpdate();
              };

              // Set up message listener for payment success
              const messageListener = (event) => {
                // Verify the origin to ensure it's from our success page
                if (event.origin === "https://ycg-frontend.vercel.app") {
                  console.log("[BUY-CREDITS] Received message from:", event.origin, event.data);

                  // Check if this is a payment success message
                  if (event.data && event.data.type === "payment_success") {
                    console.log("[BUY-CREDITS] Payment success message received");

                    // Remove the listener to avoid duplicate processing
                    window.removeEventListener("message", messageListener);

                    // Handle the payment success
                    handlePaymentSuccess();
                  }
                }
              };

              // Add the message listener
              window.addEventListener("message", messageListener);

              // Also set up a focus event as a fallback
              window.addEventListener("focus", function onFocus() {
                console.log("[BUY-CREDITS] Window regained focus, checking if we need to refresh credits");

                // Only remove this listener after one execution
                window.removeEventListener("focus", onFocus);

                // Check if we're on the success page
                chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                  if (tabs && tabs[0] && tabs[0].url && tabs[0].url.includes("payment-success.html")) {
                    console.log("[BUY-CREDITS] Success page detected via focus event");
                    handlePaymentSuccess();
                  } else {
                    console.log("[BUY-CREDITS] Not on success page, no action taken");
                  }
                });
              });
            } else {
              alert("Failed to create Stripe checkout session. Please try again.");
            }
          } catch (e) {
            console.error("[BUY-CREDITS] Error during checkout:", e);
            alert("Error: " + (e.message || e));
          } finally {
            btn.disabled = false;
            // Restore label based on current mode
            btn.textContent = `Purchase`;
          }
        });
      });
    }
  }
})();
