"use strict";

/**
 * YouTube Chapter Generator State Management Module
 *
 * This module provides a centralized state management system for the extension.
 * It uses a simple pub/sub pattern to notify components of state changes.
 */

// Import reducers
import authReducer from './reducers/auth.js';
import videoReducer from './reducers/video.js';
import chaptersReducer from './reducers/chapters.js';
import creditsReducer from './reducers/credits.js';
import uiReducer from './reducers/ui.js';

// Import middleware
import createStorageMiddleware from './middleware/storage.js';

// Add global DEBUG flag if not present
if (typeof window.YCG_DEBUG === "undefined") window.YCG_DEBUG = false;

// Define the initial state
const initialState = {
  // Auth state
  auth: {
    isAuthenticated: false,
    user: null,
    token: null,
    refreshToken: null,
    isLoading: false,
    error: null,
  },

  // Video state
  video: {
    id: null,
    title: null,
    isOnVideoPage: false,
    error: null,
    isConnectionError: false,
  },

  // Chapters state
  chapters: {
    versions: [],
    currentVersionIndex: 0,
    isGenerating: false,
    error: null,
  },

  // Credits state
  credits: {
    count: 0,
    isLoading: false,
    error: null,
  },

  // UI state
  ui: {
    activeView: "welcome", // 'welcome', 'auth', 'main'
    isMenuOpen: false,
    notifications: [],
  },
};

// Create the state store
class Store {
  constructor(initialState) {
    this.state = { ...initialState };
    this.listeners = [];
    this.reducers = {};
    this.middleware = [];
  }

  /**
   * Get the current state
   * @returns {Object} The current state
   */
  getState() {
    return { ...this.state };
  }

  /**
   * Register a reducer function for a specific state slice
   * @param {string} sliceName - The name of the state slice
   * @param {Function} reducer - The reducer function
   */
  registerReducer(sliceName, reducer) {
    this.reducers[sliceName] = reducer;
  }

  /**
   * Register middleware
   * @param {Function} middleware - The middleware function
   */
  use(middleware) {
    this.middleware.push(middleware);
  }

  /**
   * Dispatch an action to update the state
   * @param {string} sliceName - The name of the state slice to update
   * @param {Object} action - The action object with type and payload
   */
  async dispatch(sliceName, action) {
    if (window.YCG_DEBUG) console.log(`[STORE-DEBUG] Dispatching action to ${sliceName}:`, action);

    if (!this.reducers[sliceName]) {
      console.error(`[Store] No reducer registered for slice: ${sliceName}`);
      return;
    }

    const currentSliceState = this.state[sliceName];
    if (window.YCG_DEBUG) console.log(`[STORE-DEBUG] Current state for ${sliceName}:`, currentSliceState);

    // Apply middleware chain
    let finalAction = action;
    let applyReducer = true;

    // Create a next function for middleware
    const next = (sliceName, action) => {
      return { sliceName, action };
    };

    // Apply middleware
    if (this.middleware.length > 0) {
      for (const middleware of this.middleware) {
        try {
          const result = await middleware(sliceName, finalAction, next);
          if (!result) {
            applyReducer = false;
            break;
          }
          if (result.action) {
            finalAction = result.action;
          }
        } catch (error) {
          console.error(`[Store] Middleware error:`, error);
        }
      }
    }

    // Only apply reducer if middleware chain didn't cancel
    if (applyReducer) {
      const newSliceState = this.reducers[sliceName](currentSliceState, finalAction);
      if (window.YCG_DEBUG) console.log(`[STORE-DEBUG] New state for ${sliceName}:`, newSliceState);

      // Update the state
      this.state = {
        ...this.state,
        [sliceName]: newSliceState,
      };

      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Full state after update:", this.state);

      // Notify listeners
      if (window.YCG_DEBUG) console.log(`[STORE-DEBUG] Notifying ${this.listeners.length} listeners`);
      this.notifyListeners();
    }
  }

  /**
   * Subscribe to state changes
   * @param {Function} listener - The listener function
   * @returns {Function} A function to unsubscribe
   */
  subscribe(listener) {
    this.listeners.push(listener);

    // Return unsubscribe function
    return () => {
      this.listeners = this.listeners.filter((l) => l !== listener);
    };
  }

  /**
   * Notify all listeners of state changes
   */
  notifyListeners() {
    this.listeners.forEach((listener) => listener(this.state));
  }

  /**
   * Reset the state to initial values
   */
  reset() {
    this.state = { ...initialState };
    this.notifyListeners();
  }

  /**
   * Save the state to chrome.storage.local
   * @returns {Promise<boolean>} Whether the save was successful
   */
  async saveToStorage() {
    try {
      // Check if chrome is defined (running in extension context)
      if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
        const currentTime = Date.now();

        // Only save persistent parts of the state
        const persistentState = {
          auth: {
            isAuthenticated: this.state.auth.isAuthenticated,
            user: this.state.auth.user,
            token: this.state.auth.token,
            refreshToken: this.state.auth.refreshToken,
          },
          credits: {
            count: this.state.credits.count,
          },
          // Add timestamp for verification
          timestamp: currentTime
        };

        // Save video information if available, regardless of chapters
        if (this.state.video.id) {
          persistentState.video = {
            id: this.state.video.id,
            title: this.state.video.title,
            isOnVideoPage: this.state.video.isOnVideoPage,
            error: this.state.video.error,
            isConnectionError: this.state.video.isConnectionError
          };
        }

        // Save chapters data if available
        if (this.state.chapters.versions.length > 0) {
          persistentState.chapters = {
            versions: this.state.chapters.versions,
            currentVersionIndex: this.state.chapters.currentVersionIndex,
            // Add timestamp for chapters to implement 24-hour expiration
            chaptersTimestamp: currentTime
          };
        }

        // Save the state
        await chrome.storage.local.set({ ycg_state: persistentState });

        // Verify the save was successful
        try {
          const result = await chrome.storage.local.get("ycg_state");
          const savedState = result.ycg_state;

          if (!savedState) {
            console.error("[Store] State verification failed after save - no state found");
            return false;
          }

          // Don't strictly compare timestamps as they might be slightly different due to serialization
          // Just check if the state was saved at all
          if (window.YCG_DEBUG) console.log("[Store] State saved and verified in storage");
          return true;
        } catch (verifyError) {
          console.error("[Store] Error verifying saved state:", verifyError);
          return false;
        }
      } else {
        console.warn("[Store] chrome.storage.local is not available. State not saved.");
        return false;
      }
    } catch (error) {
      console.error("[Store] Error saving state to storage:", error);
      return false;
    }
  }

  /**
   * Load the state from chrome.storage.local
   * @returns {Promise<boolean>} Whether the load was successful
   */
  async loadFromStorage() {
    try {
      if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
        const result = await new Promise((resolve) => {
          chrome.storage.local.get(["ycg_state"], resolve);
        });
        if (result && result.ycg_state) {
          // Normalize user object if legacy nested format is detected
          let loadedState = result.ycg_state;
          if (loadedState.auth && loadedState.auth.user && loadedState.auth.user.data) {
            if (window.YCG_DEBUG) {
              console.debug("[STATE] Legacy user object detected in storage, normalizing to flat format.", JSON.stringify(loadedState.auth.user));
            }
            loadedState.auth.user = {
              ...loadedState.auth.user.data,
              fallback: loadedState.auth.user.fallback,
              success: loadedState.auth.user.success
            };
          }

          // Check if chapters have expired (24 hours)
          const currentTime = Date.now();
          const ONE_DAY_MS = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

          // Check if chapters exist and have expired
          let chaptersExpired = false;
          if (loadedState.chapters && loadedState.chapters.chaptersTimestamp) {
            const chaptersAge = currentTime - loadedState.chapters.chaptersTimestamp;

            if (chaptersAge > ONE_DAY_MS) {
              if (window.YCG_DEBUG) {
                console.log("[STATE] Chapters have expired (older than 24 hours). Removing chapters and video from loaded state.");
              }
              // Mark chapters as expired
              chaptersExpired = true;
              // Remove expired chapters
              delete loadedState.chapters;
            } else if (window.YCG_DEBUG) {
              console.log("[STATE] Loaded chapters are still valid (age: " +
                Math.round(chaptersAge / (60 * 60 * 1000)) + " hours)");
            }
          }

          // If chapters have expired, also remove video information
          // This ensures video info is only shown when valid chapters exist or no chapters exist yet
          if (chaptersExpired && loadedState.video) {
            delete loadedState.video;
          }

          // Check if we're on a different video page than the one with stored chapters
          // If we have a current video ID (from content script) and it's different from the stored one,
          // and we have chapters for the stored video, then don't load the chapters
          const currentVideoId = this.state.video && this.state.video.id;
          const storedVideoId = loadedState.video && loadedState.video.id;
          const hasStoredChapters = loadedState.chapters &&
                                   loadedState.chapters.versions &&
                                   loadedState.chapters.versions.length > 0;

          if (currentVideoId && storedVideoId && currentVideoId !== storedVideoId && hasStoredChapters) {
            if (window.YCG_DEBUG) {
              console.log("[STATE] Current video ID doesn't match stored video with chapters. Not loading chapters.");
              console.log(`[STATE] Current: ${currentVideoId}, Stored: ${storedVideoId}`);
            }
            // Don't load chapters for a different video
            delete loadedState.chapters;
          }

          // Prepare state to merge
          const stateToMerge = {};

          // Always merge auth and credits
          if (loadedState.auth) stateToMerge.auth = loadedState.auth;
          if (loadedState.credits) stateToMerge.credits = loadedState.credits;

          // Only merge video if we don't already have a video ID from content script
          // This ensures that the current video takes precedence over the stored one
          if (loadedState.video && (!currentVideoId || currentVideoId === storedVideoId)) {
            stateToMerge.video = loadedState.video;
          }

          // Only merge chapters if they exist and are valid
          if (loadedState.chapters) {
            stateToMerge.chapters = {
              versions: loadedState.chapters.versions || [],
              currentVersionIndex: loadedState.chapters.currentVersionIndex || 0,
              isGenerating: false,
              error: null
            };
          }

          // Merge loaded state into current state
          this.state = {
            ...this.state,
            ...stateToMerge,
          };

          this.notifyListeners();
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error("[STATE] Error loading state from storage:", error);
      return false;
    }
  }

  /**
   * Check if a state object has valid format
   * @param {Object} state - The state to validate
   * @returns {boolean} - Whether the state is valid
   */
  isValidState(state) {
    // Basic structure validation
    if (!state || typeof state !== "object") return false;

    // Check auth state
    if (!state.auth || typeof state.auth !== "object") return false;

    // Check credits state
    if (!state.credits || typeof state.credits !== "object") return false;

    return true;
  }

  /**
   * Check if a token is expired
   * @param {string} token - The JWT token to check
   * @returns {boolean} - Whether the token is expired
   */
  isTokenExpired(token) {
    try {
      // Parse the JWT token
      const base64Url = token.split(".")[1];
      if (!base64Url) return true;

      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(atob(base64).split("").map(function(c) {
        return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(""));

      const payload = JSON.parse(jsonPayload);

      // Check if token has expiration
      if (!payload.exp) return false;

      // Check if token is expired
      const currentTime = Math.floor(Date.now() / 1000);
      return currentTime > payload.exp;
    } catch (error) {
      console.error("[Store] Error checking token expiration:", error);
      return true; // Assume expired if we can't parse it
    }
  }
}

// Create and initialize the store
const store = new Store(initialState);

// Register reducers
store.registerReducer("auth", authReducer);
store.registerReducer("video", videoReducer);
store.registerReducer("chapters", chaptersReducer);
store.registerReducer("credits", creditsReducer);
store.registerReducer("ui", uiReducer);

// Register middleware
store.use(createStorageMiddleware(store));

// Add beforeunload event listener to save state when popup closes
window.addEventListener("beforeunload", async () => {
  if (window.YCG_DEBUG) console.log("[Store] Popup closing, saving state...");
  await store.saveToStorage();
  if (window.YCG_DEBUG) console.log("[Store] State saved before popup close");
});

// Export the store
window.YCG_STORE = store;

export default store;
