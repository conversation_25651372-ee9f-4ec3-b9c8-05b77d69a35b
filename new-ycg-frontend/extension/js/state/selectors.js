"use strict";

/**
 * State selectors
 * 
 * Functions to extract and derive data from the state
 */

// Auth selectors
export const getAuthState = (state) => state.auth;
export const isAuthenticated = (state) => state.auth.isAuthenticated;
export const getUser = (state) => state.auth.user;
export const getToken = (state) => state.auth.token;
export const getAuthError = (state) => state.auth.error;
export const isAuthLoading = (state) => state.auth.isLoading;

// Video selectors
export const getVideoState = (state) => state.video;
export const getVideoId = (state) => state.video.id;
export const getVideoTitle = (state) => state.video.title;
export const isOnVideoPage = (state) => state.video.isOnVideoPage;
export const getVideoError = (state) => state.video.error;

// Chapters selectors
export const getChaptersState = (state) => state.chapters;
export const getCurrentChapters = (state) => {
  const { versions, currentVersionIndex } = state.chapters;
  return versions.length > 0 ? versions[currentVersionIndex] : null;
};
export const getChaptersVersions = (state) => state.chapters.versions;
export const getCurrentVersionIndex = (state) => state.chapters.currentVersionIndex;
export const isGeneratingChapters = (state) => state.chapters.isGenerating;
export const getChaptersError = (state) => state.chapters.error;
export const hasChapters = (state) => state.chapters.versions.length > 0;

// Credits selectors
export const getCreditsState = (state) => state.credits;
export const getCreditsCount = (state) => state.credits.count;
export const isLoadingCredits = (state) => state.credits.isLoading;
export const getCreditsError = (state) => state.credits.error;

// UI selectors
export const getUiState = (state) => state.ui;
export const getActiveView = (state) => state.ui.activeView;
export const isMenuOpen = (state) => state.ui.isMenuOpen;
export const getNotifications = (state) => state.ui.notifications;

// Export all selectors as a named object for easy access
const selectors = {
  // Auth
  getAuthState,
  isAuthenticated,
  getUser,
  getToken,
  getAuthError,
  isAuthLoading,
  
  // Video
  getVideoState,
  getVideoId,
  getVideoTitle,
  isOnVideoPage,
  getVideoError,
  
  // Chapters
  getChaptersState,
  getCurrentChapters,
  getChaptersVersions,
  getCurrentVersionIndex,
  isGeneratingChapters,
  getChaptersError,
  hasChapters,
  
  // Credits
  getCreditsState,
  getCreditsCount,
  isLoadingCredits,
  getCreditsError,
  
  // UI
  getUiState,
  getActiveView,
  isMenuOpen,
  getNotifications
};

// Make selectors available globally
window.YCG_SELECTORS = selectors;
