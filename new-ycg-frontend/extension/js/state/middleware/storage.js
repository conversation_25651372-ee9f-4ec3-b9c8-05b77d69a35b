"use strict";

/**
 * Storage middleware
 *
 * Middleware for persisting state to chrome.storage.local
 */

// Actions that should trigger state persistence
const PERSIST_ACTIONS = [
  // Auth actions
  "LOGIN_SUCCESS",
  "LOGOUT",
  "UPDATE_USER",
  "UPDATE_TOKENS",

  // Credits actions
  "SET_CREDITS",

  // Chapters actions
  "GENERATE_SUCCESS",
  "CLEAR_CHAPTERS",

  // Video actions
  "SET_VIDEO_INFO",
  "C<PERSON>AR_VIDEO_INFO"
];

/**
 * Creates a storage middleware function
 * @param {Object} store - The store instance
 * @returns {Function} - The middleware function
 */
export default function createStorageMiddleware(store) {
  return async (sliceName, action, next) => {
    // Let the action go through to the reducer first
    if (window.YCG_DEBUG) console.log(`[STORAGE-DEBUG] Middleware processing action: ${action.type} for slice: ${sliceName}`);

    const result = next(sliceName, action);

    // Then save the state to storage for specific actions
    if (PERSIST_ACTIONS.includes(action.type)) {
      if (window.YCG_DEBUG) console.log(`[STORAGE-DEBUG] Persisting state after action: ${action.type}`);

      // Log the current state before saving
      if (window.YCG_DEBUG && sliceName === 'auth' && action.type === 'UPDATE_TOKENS') {
        const currentState = store.getState();
        console.log(`[STORAGE-DEBUG] State before saving:`, {
          auth: {
            isAuthenticated: currentState.auth.isAuthenticated,
            hasToken: !!currentState.auth.token,
            hasRefreshToken: !!currentState.auth.refreshToken,
            hasUser: !!currentState.auth.user
          }
        });
      }

      await store.saveToStorage();

      // Log the state after saving
      if (window.YCG_DEBUG && sliceName === 'auth' && action.type === 'UPDATE_TOKENS') {
        console.log(`[STORAGE-DEBUG] State saved to storage after ${action.type}`);
      }
    }

    return result;
  };
}
