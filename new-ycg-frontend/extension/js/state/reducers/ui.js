"use strict";

/**
 * UI reducer
 * 
 * Handles state updates for UI-related actions
 */
export default function uiReducer(state, action) {
  if (window.YCG_DEBUG) console.log("[STORE-DEBUG] UI reducer called with action:", action.type);
  if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Current UI state:", state);

  switch (action.type) {
    case "SET_ACTIVE_VIEW":
      const setActiveViewState = {
        ...state,
        activeView: action.payload.view,
      };
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] New UI state after SET_ACTIVE_VIEW:", setActiveViewState);
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Active view changed to:", action.payload.view);
      return setActiveViewState;

    case "TOGGLE_MENU":
      return {
        ...state,
        isMenuOpen: !state.isMenuOpen,
      };
    case "CLOSE_MENU":
      return {
        ...state,
        isMenuOpen: false,
      };
    case "ADD_NOTIFICATION":
      return {
        ...state,
        notifications: [...state.notifications, action.payload.notification],
      };
    case "REMOVE_NOTIFICATION":
      return {
        ...state,
        notifications: state.notifications.filter((n) => n.id !== action.payload.id),
      };
    case "CLEAR_NOTIFICATIONS":
      return {
        ...state,
        notifications: [],
      };
    default:
      return state;
  }
}
