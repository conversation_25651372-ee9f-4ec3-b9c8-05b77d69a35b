"use strict";

/**
 * Authentication reducer
 *
 * Handles state updates for authentication-related actions
 */
export default function authReducer(state, action) {
  if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Auth reducer called with action:", action.type);
  if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Current auth state:", state);

  switch (action.type) {
    case "LOGIN_START":
      const loginStartState = { ...state, isLoading: true, error: null };
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] New auth state after LOGIN_START:", loginStartState);
      return loginStartState;

    case "LOGIN_SUCCESS":
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] LOGIN_SUCCESS payload refreshToken:", action.payload.refreshToken); // Added this log
      const loginSuccessState = {
        ...state,
        isLoading: false,
        user: action.payload.user, // Ensure user object contains id
        token: action.payload.token,
        refreshToken: action.payload.refreshToken, // Store refreshToken
        isAuthenticated: true,
        error: null
      };
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] New auth state after LOGIN_SUCCESS:", loginSuccessState);
      return loginSuccessState;

    case "LOGIN_FAILURE":
      const loginFailureState = { ...state, isLoading: false, error: action.payload.error };
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] New auth state after LOGIN_FAILURE:", loginFailureState);
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Error:", action.payload.error);
      return loginFailureState;

    case "LOGOUT":
      const logoutState = { ...state, isAuthenticated: false, user: null, token: null, refreshToken: null, error: null };
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] New auth state after LOGOUT:", logoutState);
      return logoutState;

    case "UPDATE_USER":
      const updateUserState = { ...state, user: action.payload.user };
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] New auth state after UPDATE_USER:", updateUserState);
      return updateUserState;

    case "UPDATE_TOKENS":
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Auth reducer called with action: UPDATE_TOKENS");
      if (window.YCG_DEBUG) console.log("[STORE-DEBUG] Current auth state:", {
        isAuthenticated: state.isAuthenticated,
        hasUser: !!state.user,
        hasToken: !!state.token,
        hasRefreshToken: !!state.refreshToken
      });

      if (window.YCG_DEBUG) {
        if (action.payload.token) {
          console.log("[STORE-DEBUG] UPDATE_TOKENS payload token:", action.payload.token.substring(0, 10) + "...");
        } else {
          console.log("[STORE-DEBUG] UPDATE_TOKENS payload token: missing");
        }

        if (action.payload.refreshToken) {
          console.log("[STORE-DEBUG] UPDATE_TOKENS payload refreshToken:", action.payload.refreshToken.substring(0, 10) + "...");
        } else {
          console.log("[STORE-DEBUG] UPDATE_TOKENS payload refreshToken: missing");
        }
      }

      // Make sure we have both tokens before updating
      if (!action.payload.token || !action.payload.refreshToken) {
        console.error("[STORE-DEBUG] Missing token or refreshToken in UPDATE_TOKENS payload", {
          hasToken: !!action.payload.token,
          hasRefreshToken: !!action.payload.refreshToken
        });
        return state;
      }

      const updateTokensState = {
        ...state,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken
      };

      if (window.YCG_DEBUG) {
        console.log("[STORE-DEBUG] New auth state after UPDATE_TOKENS:", {
          isAuthenticated: updateTokensState.isAuthenticated,
          hasUser: !!updateTokensState.user,
          hasToken: !!updateTokensState.token,
          hasRefreshToken: !!updateTokensState.refreshToken
        });

        if (updateTokensState.token) {
          console.log("[STORE-DEBUG] New token:", updateTokensState.token.substring(0, 10) + "...");
        }

        if (updateTokensState.refreshToken) {
          console.log("[STORE-DEBUG] New refreshToken:", updateTokensState.refreshToken.substring(0, 10) + "...");
        }
      }

      return updateTokensState;

    default:
      return state;
  }
}
