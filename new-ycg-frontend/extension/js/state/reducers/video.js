"use strict";

/**
 * Video reducer
 *
 * Handles state updates for video-related actions
 */
export default function videoReducer(state, action) {
  switch (action.type) {
    case "SET_VIDEO_INFO":
      return {
        ...state,
        id: action.payload.id,
        title: action.payload.title,
        isOnVideoPage: true,
        error: null,
      };
    case "CLEAR_VIDEO_INFO":
      return {
        ...state,
        id: null,
        title: null,
        isOnVideoPage: false,
        error: null,
      };
    case "SET_VIDEO_ERROR":
      return {
        ...state,
        error: action.payload.error,
        isOnVideoPage: false,
        isConnectionError: action.payload.isConnectionError || false,
      };
    default:
      return state;
  }
}
