"use strict";

/**
 * Chapters reducer
 * 
 * Handles state updates for chapters-related actions
 */
export default function chaptersReducer(state, action) {
  switch (action.type) {
    case "GENERATE_START":
      return {
        ...state,
        isGenerating: true,
        error: null,
      };
    case "GENERATE_SUCCESS":
      // Add new version to versions array
      let newVersions = state.versions;
      const newChapters = action.payload.chapters;
      // Always push a new version, even if identical
      newVersions = [...state.versions, newChapters];
      return {
        ...state,
        versions: newVersions,
        currentVersionIndex: newVersions.length - 1,
        isGenerating: false,
        error: null,
      };
    case "GENERATE_FAILURE":
      return {
        ...state,
        isGenerating: false,
        error: action.payload.error,
      };
    case "SET_VERSION_INDEX":
      return {
        ...state,
        currentVersionIndex: action.payload.index,
      };
    case "CLEAR_CHAPTERS":
      return {
        ...state,
        versions: [],
        currentVersionIndex: 0,
        isGenerating: false,
        error: null,
      };
    default:
      return state;
  }
}
