"use strict";

/**
 * Credits reducer
 * 
 * Handles state updates for credits-related actions
 */
export default function creditsReducer(state, action) {
  switch (action.type) {
    case "SET_CREDITS":
      return {
        ...state,
        count: action.payload.count,
        isLoading: false,
        error: null,
      };
    case "LOADING_CREDITS":
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case "CREDITS_ERROR":
      return {
        ...state,
        isLoading: false,
        error: action.payload.error,
      };
    case "DECREMENT_CREDITS":
      return {
        ...state,
        count: Math.max(0, state.count - 1),
      };
    default:
      return state;
  }
}
