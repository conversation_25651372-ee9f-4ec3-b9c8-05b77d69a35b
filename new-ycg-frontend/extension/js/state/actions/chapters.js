"use strict";

/**
 * Chapters action creators
 */

// Generate chapters actions
export const generateStart = () => ({
  type: "GENERATE_START"
});

export const generateSuccess = (chapters) => ({
  type: "GENERATE_SUCCESS",
  payload: { chapters }
});

export const generateFailure = (error) => ({
  type: "GENERATE_FAILURE",
  payload: { error }
});

// Version control actions
export const setVersionIndex = (index) => ({
  type: "SET_VERSION_INDEX",
  payload: { index }
});

// Clear chapters action
export const clearChapters = () => ({
  type: "CLEAR_CHAPTERS"
});

// Export all actions as a named object for easy access
const chaptersActions = {
  generateStart,
  generateSuccess,
  generateFailure,
  setVersionIndex,
  clearChapters
};

// Make actions available globally
window.YCG_CHAPTERS_ACTIONS = chaptersActions;
