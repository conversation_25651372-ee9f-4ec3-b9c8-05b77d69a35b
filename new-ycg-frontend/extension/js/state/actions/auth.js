"use strict";

/**
 * Authentication action creators
 */

// Login actions
export const loginStart = () => ({
  type: "LOGIN_START"
});

export const loginSuccess = (user, token, refreshToken) => ({
  type: "LOGIN_SUCCESS",
  payload: { user, token, refreshToken }
});

export const loginFailure = (error) => ({
  type: "LOGIN_FAILURE",
  payload: { error }
});

// Logout action
export const logout = () => ({
  type: "LOGOUT"
});

// Update user action
export const updateUser = (user) => ({
  type: "UPDATE_USER",
  payload: { user }
});

// Update tokens action
export const updateTokens = (token, refreshToken) => ({
  type: "UPDATE_TOKENS",
  payload: { token, refreshToken }
});

// Export all actions as a named object for easy access
const authActions = {
  loginStart,
  loginSuccess,
  loginFailure,
  logout,
  updateUser,
  updateTokens
};

// Make actions available globally
window.YCG_AUTH_ACTIONS = authActions;
