"use strict";

/**
 * UI action creators
 */

// Set active view action
export const setActiveView = (view) => ({
  type: "SET_ACTIVE_VIEW",
  payload: { view }
});

// Menu actions
export const toggleMenu = () => ({
  type: "TOGGLE_MENU"
});

export const closeMenu = () => ({
  type: "CLOSE_MENU"
});

// Notification actions
export const addNotification = (notification) => ({
  type: "ADD_NOTIFICATION",
  payload: { notification }
});

export const removeNotification = (id) => ({
  type: "REMOVE_NOTIFICATION",
  payload: { id }
});

export const clearNotifications = () => ({
  type: "CLEAR_NOTIFICATIONS"
});

// Export all actions as a named object for easy access
const uiActions = {
  setActiveView,
  toggleMenu,
  closeMenu,
  addNotification,
  removeNotification,
  clearNotifications
};

// Make actions available globally
window.YCG_UI_ACTIONS = uiActions;
