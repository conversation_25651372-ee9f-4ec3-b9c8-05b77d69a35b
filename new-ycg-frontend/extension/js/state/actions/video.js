"use strict";

/**
 * Video action creators
 */

// Set video info action
export const setVideoInfo = (id, title) => ({
  type: "SET_VIDEO_INFO",
  payload: { id, title }
});

// Clear video info action
export const clearVideoInfo = () => ({
  type: "CLEAR_VIDEO_INFO"
});

// Set video error action
export const setVideoError = (error) => ({
  type: "SET_VIDEO_ERROR",
  payload: { error }
});

// Export all actions as a named object for easy access
const videoActions = {
  setVideoInfo,
  clearVideoInfo,
  setVideoError
};

// Make actions available globally
window.YCG_VIDEO_ACTIONS = videoActions;
