"use strict";

/**
 * Credits action creators
 */

// Set credits action
export const setCredits = (count) => ({
  type: "SET_CREDITS",
  payload: { count }
});

// Loading credits action
export const loadingCredits = () => ({
  type: "LOADING_CREDITS"
});

// Credits error action
export const creditsError = (error) => ({
  type: "CREDITS_ERROR",
  payload: { error }
});

// Decrement credits action
export const decrementCredits = () => ({
  type: "DECREMENT_CREDITS"
});

// Export all actions as a named object for easy access
const creditsActions = {
  setCredits,
  loadingCredits,
  creditsError,
  decrementCredits
};

// Make actions available globally
window.YCG_CREDITS_ACTIONS = creditsActions;
