/**
 * YouTube Chapter Generator Video Service Module
 *
 * This module provides a service for interacting with YouTube videos.
 * It handles communication with the content script to get video information.
 */

// Add global DEBUG flag if not present
if (typeof window.YCG_DEBUG === "undefined") window.YCG_DEBUG = false;

/**
 * Video Service class for interacting with YouTube videos
 */
class VideoService {
  constructor() {
    this.store = window.YCG_STORE;
    this.lastCheckTime = 0;
    this.checkInterval = 1000; // 1 second
    this.isInitialized = false;
    this.authStateListener = this.handleAuthStateChange.bind(this);
  }

  /**
   * Initialize the video service
   */
  init() {
    if (this.isInitialized) {
      if (window.YCG_DEBUG) console.log("[Video] Video service already initialized");
      return;
    }

    if (window.YCG_DEBUG) console.log("[Video] Initializing video service");

    // Set up auth state change listener
    if (this.store) {
      if (window.YCG_DEBUG) console.log("[Video] Setting up auth state change listener");
      this.store.subscribe(this.authStateListener);
    }

    // Only check for video if user is logged in
    if (this.isUserLoggedIn()) {
      if (window.YCG_DEBUG) console.log("[Video] User is logged in, checking for video");
      this.checkForVideo();
    } else {
      if (window.YCG_DEBUG) console.log("[Video] User is not logged in, skipping video check");
    }

    this.isInitialized = true;
    if (window.YCG_DEBUG) console.log("[Video] Video service initialized");
  }

  /**
   * Handle auth state changes
   */
  handleAuthStateChange() {
    if (!this.store) return;

    const state = this.store.getState();
    const isLoggedIn = state && state.auth && state.auth.isAuthenticated && state.auth.token;

    // Check for video when user logs in
    if (isLoggedIn) {
      if (window.YCG_DEBUG) console.log("[Video] Auth state changed: User is logged in, checking for video");
      this.checkForVideo();
    } else {
      if (window.YCG_DEBUG) console.log("[Video] Auth state changed: User is not logged in");
    }
  }

  /**
   * Check if the user is logged in
   * @returns {boolean} Whether the user is logged in
   */
  isUserLoggedIn() {
    if (!this.store) return false;

    const state = this.store.getState();
    return state && state.auth && state.auth.isAuthenticated && state.auth.token;
  }

  /**
   * Check if the current tab is a YouTube video page
   * @returns {Promise<void>}
   */
  async checkForVideo() {
    // Throttle checks to avoid too many messages
    const now = Date.now();
    if (now - this.lastCheckTime < this.checkInterval) {
      return;
    }
    this.lastCheckTime = now;

    if (window.YCG_DEBUG) console.log("[Video] Checking for YouTube video");

    try {
      // Query for the active tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentTab = tabs[0];

      if (!currentTab) {
        if (window.YCG_DEBUG) console.log("[Video] No active tab found");
        this.store.dispatch("video", {
          type: "SET_VIDEO_ERROR",
          payload: {
            error: "Please open a YouTube video page in your browser."
          },
        });

        // Save state to storage to persist the error message
        await this.store.saveToStorage();
        return;
      }

      // Check if the tab is a YouTube page
      const isYouTube = currentTab.url && currentTab.url.includes("youtube.com");

      if (!isYouTube) {
        if (window.YCG_DEBUG) console.log("[Video] Not a YouTube page:", currentTab.url);
        this.store.dispatch("video", {
          type: "SET_VIDEO_ERROR",
          payload: { error: "Please navigate to a YouTube video page" },
        });

        // Save state to storage to persist the error message
        await this.store.saveToStorage();
        return;
      }

      // First, ensure the content script is injected by asking the background script
      if (window.YCG_DEBUG) console.log("[Video] Ensuring content script is injected");

      try {
        const contentScriptResult = await new Promise((resolve) => {
          chrome.runtime.sendMessage({ action: "ensureContentScriptInjected" }, (response) => {
            if (chrome.runtime.lastError) {
              if (window.YCG_DEBUG) console.log("[Video] Error ensuring content script:", chrome.runtime.lastError.message);
              resolve({ success: false, error: chrome.runtime.lastError.message });
            } else {
              resolve(response || { success: false, error: "No response from background script" });
            }
          });
        });

        if (window.YCG_DEBUG) console.log("[Video] Content script injection result:", contentScriptResult);
      } catch (error) {
        if (window.YCG_DEBUG) console.log("[Video] Error communicating with background script:", error);
        // Continue anyway, we'll handle connection errors below
      }

      // Create a promise that will reject after a timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error("Content script communication timed out"));
        }, 5000); // 5 second timeout
      });

      // Create the message promise
      const messagePromise = new Promise((resolve) => {
        chrome.tabs.sendMessage(currentTab.id, { action: "getVideoInfo" }, (response) => {
          // Check for chrome runtime error
          if (chrome.runtime.lastError) {
            if (window.YCG_DEBUG) console.log("[Video] Chrome runtime error:", chrome.runtime.lastError.message);
            resolve({
              success: false,
              error: chrome.runtime.lastError.message,
              isConnectionError: chrome.runtime.lastError.message.includes("Could not establish connection") ||
                                chrome.runtime.lastError.message.includes("Receiving end does not exist")
            });
          } else {
            resolve(response || { success: false, error: "No response from content script" });
          }
        });
      });

      // Race the message against the timeout
      const response = await Promise.race([messagePromise, timeoutPromise]);

      if (!response || !response.success) {
        const error = response?.error || "Failed to get video information";
        const isConnectionError = response?.isConnectionError || false;

        if (window.YCG_DEBUG) console.log("[Video] Video check failed:", { error, isConnectionError });

        // Set a more user-friendly error message for connection errors
        let errorMessage = error;
        if (isConnectionError) {
          // Try to inject the content script again
          try {
            const contentScriptResult = await new Promise((resolve) => {
              chrome.runtime.sendMessage({ action: "ensureContentScriptInjected" }, (response) => {
                if (chrome.runtime.lastError) {
                  resolve({ success: false, error: chrome.runtime.lastError.message });
                } else {
                  resolve(response || { success: false, error: "No response from background script" });
                }
              });
            });

            if (contentScriptResult && contentScriptResult.success) {
              if (window.YCG_DEBUG) console.log("[Video] Content script injected successfully, retrying video check");

              // Wait a moment for the content script to initialize
              await new Promise(resolve => setTimeout(resolve, 500));

              // Try again to get video info
              return this.checkForVideo();
            } else {
              errorMessage = "Loading YouTube video information... If this message persists, please refresh the page.";
            }
          } catch (injectionError) {
            if (window.YCG_DEBUG) console.log("[Video] Error during content script injection retry:", injectionError);
            errorMessage = "Loading YouTube video information... If this message persists, please refresh the page.";
          }
        }

        this.store.dispatch("video", {
          type: "SET_VIDEO_ERROR",
          payload: {
            error: errorMessage,
            isConnectionError: isConnectionError
          },
        });

        // Save state to storage to persist the error message
        await this.store.saveToStorage();
        return;
      }

      // Check if this is a different video than the one with chapters
      const currentState = this.store.getState();
      const currentChapters = currentState.chapters;
      const previousVideoId = currentState.video.id;

      // If we have chapters for a different video, clear them
      if (previousVideoId &&
          previousVideoId !== response.videoId &&
          currentChapters &&
          currentChapters.versions &&
          currentChapters.versions.length > 0) {
        if (window.YCG_DEBUG) console.log("[Video] New video detected, clearing chapters for previous video:", previousVideoId);

        // Clear chapters for the previous video
        this.store.dispatch("chapters", { type: "CLEAR_CHAPTERS" });
      }

      // Update video info in store
      if (window.YCG_DEBUG) console.log("[Video] Dispatching SET_VIDEO_INFO", response.videoId, response.videoTitle);
      this.store.dispatch("video", {
        type: "SET_VIDEO_INFO",
        payload: {
          id: response.videoId,
          title: response.videoTitle,
        },
      });

      // Save state to storage to persist the video information
      // This ensures video info is available when popup is reopened
      await this.store.saveToStorage();

      if (window.YCG_DEBUG) console.log("[Video] State after dispatch:", this.store.getState().video);
      if (window.YCG_DEBUG) console.log("[Video] Video found:", response.videoId, response.videoTitle);
    } catch (error) {
      // Handle all errors gracefully
      const msg = error && error.message ? error.message : String(error);
      const isConnectionError = msg.includes("Could not establish connection") ||
                               msg.includes("Receiving end does not exist") ||
                               msg.includes("Content script communication timed out");

      if (window.YCG_DEBUG) {
        console.log("[Video] Error checking for video:", {
          error: msg,
          isConnectionError
        });
      }

      // Set a more user-friendly error message
      let errorMessage = "An error occurred while checking for a video. Please try again.";

      if (isConnectionError) {
        // Try to inject the content script again
        try {
          const contentScriptResult = await new Promise((resolve) => {
            chrome.runtime.sendMessage({ action: "ensureContentScriptInjected" }, (response) => {
              if (chrome.runtime.lastError) {
                resolve({ success: false, error: chrome.runtime.lastError.message });
              } else {
                resolve(response || { success: false, error: "No response from background script" });
              }
            });
          });

          if (contentScriptResult && contentScriptResult.success) {
            if (window.YCG_DEBUG) console.log("[Video] Content script injected successfully, retrying video check");

            // Wait a moment for the content script to initialize
            await new Promise(resolve => setTimeout(resolve, 500));

            // Try again to get video info
            return this.checkForVideo();
          } else {
            errorMessage = "Loading YouTube video information... If this message persists, please refresh the page.";
          }
        } catch (injectionError) {
          if (window.YCG_DEBUG) console.log("[Video] Error during content script injection retry:", injectionError);
          errorMessage = "Loading YouTube video information... If this message persists, please refresh the page.";
        }
      }

      this.store.dispatch("video", {
        type: "SET_VIDEO_ERROR",
        payload: {
          error: errorMessage,
          isConnectionError: isConnectionError
        },
      });

      // Save state to storage to persist the error message
      await this.store.saveToStorage();
    }
  }
}

// Create and export the video service
document.addEventListener("DOMContentLoaded", () => {
  // Wait for the DOM to be loaded before creating the video service
  // This ensures that YCG_STORE is available
  if (typeof chrome !== "undefined" && chrome.tabs) {
    if (window.YCG_STORE) {
      window.YCG_VIDEO = new VideoService();
      if (window.YCG_DEBUG) console.log("[Video] Video service created");
    } else {
      console.error("[Video] Failed to create video service: YCG_STORE not available");
    }
  } else {
    if (window.YCG_DEBUG) console.warn("[Video] Chrome API not available. Running outside of extension context?");
  }
});
