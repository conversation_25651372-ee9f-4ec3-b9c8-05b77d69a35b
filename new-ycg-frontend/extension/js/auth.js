/**
 * YouTube Chapter Generator Authentication Module
 *
 * This module handles user authentication using Google OAuth.
 */

// Simplified auth utilities
async function clearAuthFlags() {
  if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
    await chrome.storage.local.remove([
      "ycg_logout_marker",
      "ycg_logout_marker_timestamp",
      "ycg_pending_signin",
      "ycg_signin_timestamp"
    ]);
    if (window.YCG_DEBUG) console.log("[Auth] All auth flags cleared");
  }
}

// Initialize auth when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  console.log("[AUTH-DEBUG] DOM content loaded, checking for store and API");

  // Try to initialize with increasing delays
  const tryInit = (attempt = 1, maxAttempts = 5) => {
    console.log(`[AUTH-DEBUG] Auth initialization attempt ${attempt}/${maxAttempts}`);

    // Check if store is available (API is optional)
    if (window.YCG_STORE) {
      console.log("[AUTH-DEBUG] Store available, initializing auth");
      // Initialize with whatever services are available
      initAuth();
      return;
    }

    if (attempt < maxAttempts) {
      const delay = 100 * Math.pow(2, attempt - 1); // Exponential backoff
      console.log(`[AUTH-DEBUG] Services not ready, retrying auth init in ${delay}ms`);
      setTimeout(() => tryInit(attempt + 1, maxAttempts), delay);
    } else {
      console.error("[AUTH-DEBUG] Failed to initialize auth after multiple attempts");
      console.log(
        "[AUTH-DEBUG] Available globals:",
        Object.keys(window).filter((key) => key.startsWith("YCG_")),
      );
    }
  };

  // Start initialization attempts
  tryInit();
});

// --- Silent Google OAuth re-authentication utility ---
async function trySilentReauth() {
  return new Promise((resolve, reject) => {
    if (!chrome.identity) return reject(new Error("chrome.identity not available"));
    chrome.identity.getAuthToken({ interactive: false }, (token) => {
      if (chrome.runtime.lastError || !token) {
        console.warn("[Auth] Silent re-auth failed:", chrome.runtime.lastError);
        reject(new Error("Silent re-auth failed"));
      } else {
        console.log("[Auth] Silent re-auth succeeded");
        resolve(token);
      }
    });
  });
}

/**
 * Initialize authentication
 */
async function initAuth() {
  console.log("[Auth] Initializing auth...");

  // Get references to the store and API
  const store = window.YCG_STORE;
  const api = window.YCG_API;

  if (!store) {
    console.error("[Auth] Store not available, cannot initialize auth");
    return;
  }

  // First, load state from storage to ensure we have the latest state
  if (window.YCG_DEBUG) console.log("[Auth] Loading state from storage");
  await store.loadFromStorage();

  // Check current auth state after loading from storage
  const state = store.getState();
  const isAuthenticated = state.auth && state.auth.isAuthenticated;
  const token = state.auth && state.auth.token;
  const refreshToken = state.auth && state.auth.refreshToken;

  if (window.YCG_DEBUG) console.log("[Auth] Current auth state after loading:", {
    isAuthenticated,
    hasToken: !!token,
    hasRefreshToken: !!refreshToken
  });

  // Clear any auth flags to ensure a clean state
  await clearAuthFlags();

  // If we're already authenticated, validate the token
  if (isAuthenticated && token && api) {
    if (window.YCG_DEBUG) console.log("[Auth] Already authenticated, validating token");

    // Parse the token to check if it's valid
    const tokenPayload = api.parseToken(token);
    const isTokenValid = tokenPayload !== null;
    const isTokenExpired = api.isTokenExpiredOrExpiringSoon(token);

    if (window.YCG_DEBUG) console.log("[Auth] Token validation:", {
      isValid: isTokenValid,
      isExpired: isTokenExpired,
      payload: tokenPayload
    });

    if (isTokenValid && !isTokenExpired) {
      // Token is valid and not expired, proceed as authenticated
      console.log("[Auth] Token is valid, proceeding as authenticated");

      // Set active view to main
      store.dispatch("ui", {
        type: "SET_ACTIVE_VIEW",
        payload: { view: "main" },
      });

      // Start token refresh monitoring
      startTokenExpiryMonitor();

      // Set up event listeners
      setupAuthEventListeners();

      console.log("[Auth] Initialization complete - authenticated");
      return;
    } else if (isTokenValid && isTokenExpired && refreshToken) {
      // Token is valid but expired - try to refresh it
      console.log("[Auth] Token is expired, attempting refresh");

      try {
        // Try to refresh the token
        if (api.refreshToken) {
          const newToken = await api.refreshToken();
          if (newToken) {
            console.log("[Auth] Successfully refreshed token");

            // Set active view to main
            store.dispatch("ui", {
              type: "SET_ACTIVE_VIEW",
              payload: { view: "main" },
            });

            // Start token refresh monitoring
            startTokenExpiryMonitor();

            // Set up event listeners
            setupAuthEventListeners();

            console.log("[Auth] Initialization complete - token refreshed");
            return;
          }
        }
      } catch (refreshError) {
        console.warn("[Auth] Token refresh failed:", refreshError);
        // Fall back to silent reauth
      }

      // Try silent reauth as a fallback
      try {
        const newToken = await trySilentReauth();
        if (newToken) {
          console.log("[Auth] Successfully refreshed token via silent reauth");

          // Exchange Google token for backend JWT + user info
          const loginResult = await api.loginWithGoogle(newToken);
          if (loginResult && loginResult.access_token) {
            // Extract user info, tokens, and credits
            const { access_token, refresh_token, user_id, email, name, picture, credits } = loginResult;

            // Update auth state
            store.dispatch("auth", {
              type: "LOGIN_SUCCESS",
              payload: {
                user: {
                  id: user_id,
                  email,
                  name,
                  picture,
                },
                token: access_token,
                refreshToken: refresh_token,
              },
            });

            // Update credits
            if (credits !== undefined) {
              store.dispatch("credits", {
                type: "SET_CREDITS",
                payload: {
                  count: credits || 0,
                },
              });
            }

            // Set active view to main
            store.dispatch("ui", {
              type: "SET_ACTIVE_VIEW",
              payload: { view: "main" },
            });

            // Save state to storage
            await store.saveToStorage();

            // Start token refresh monitoring
            startTokenExpiryMonitor();

            // Set up event listeners
            setupAuthEventListeners();

            console.log("[Auth] Initialization complete - reauthed with Google");
            return;
          }
        }
      } catch (err) {
        console.warn("[Auth] Silent reauth failed:", err);
        // Continue to Google Sign-In initialization
      }
    }
  }

  // If we're not authenticated or token validation/refresh failed, initialize Google Sign-In
  console.log("[Auth] Not authenticated or token validation failed, initializing Google Sign-In");

  // Initialize Google Sign-In
  initGoogleSignIn();

  // Set up event listeners
  setupAuthEventListeners();

  console.log("[Auth] Initialization complete - not authenticated");
}

// --- Periodic token expiry check ---
let tokenExpiryInterval = null;

function stopTokenExpiryMonitor() {
  if (tokenExpiryInterval) {
    clearInterval(tokenExpiryInterval);
    tokenExpiryInterval = null;
    console.log("[AUTH] Stopped token expiry monitor");
  }
}

function startTokenExpiryMonitor() {
  stopTokenExpiryMonitor();
  const store = window.YCG_STORE;
  const api = window.YCG_API;
  if (!store || !api) return;
  const state = store.getState();
  const token = state.auth.token;
  if (!state.auth.isAuthenticated || !token) {
    console.log("[AUTH] Not authenticated, not starting token expiry monitor");
    return;
  }

  // Check token immediately to avoid waiting for the first interval
  checkAndRefreshToken(store, api);

  // Set up the interval for periodic checks
  tokenExpiryInterval = setInterval(() => {
    checkAndRefreshToken(store, api);
  }, 5 * 60 * 1000); // Check every 5 minutes

  console.log("[AUTH] Started token expiry monitor");
}

// Separate function to check and refresh token
async function checkAndRefreshToken(store, api) {
  try {
    const state = store.getState();
    const token = state.auth.token;
    const refreshToken = state.auth.refreshToken;

    if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] checkAndRefreshToken called");
    if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Current auth state:", {
      isAuthenticated: state.auth.isAuthenticated,
      hasToken: !!token,
      hasRefreshToken: !!refreshToken,
      activeView: state.ui.activeView
    });

    // If we're authenticated but don't have a token, try to refresh anyway
    if (state.auth.isAuthenticated && !token && refreshToken) {
      if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Authenticated but no token, attempting refresh with refresh token");

      try {
        await api.refreshToken();
        if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Successfully refreshed token without initial token");
        return;
      } catch (error) {
        if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Failed to refresh token without initial token:", error);

        // Try silent Google Sign-In as a last resort
        try {
          await api.refreshViaGoogleSignIn();
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Successfully refreshed via Google Sign-In without initial token");
          return;
        } catch (googleError) {
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Failed to refresh via Google Sign-In without initial token:", googleError);
          // Continue with normal flow
        }
      }
    }

    // Skip token check if not authenticated
    if (!state.auth.isAuthenticated) {
      if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Skipping token check - not authenticated");
      return;
    }

    // Skip if we're on the welcome screen (not logged in)
    if (state.ui.activeView === "welcome") {
      if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Skipping token check - on welcome screen");
      return;
    }

    // If we have a token, check if it's expired or expiring soon
    if (token && api.isTokenExpiredOrExpiringSoon(token)) {
      if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Token expiring soon, attempting refresh");
      if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Current refreshToken:", refreshToken ? "present" : "missing");

      try {
        // Try to use the API's refreshToken method first
        if (api.refreshToken && refreshToken) {
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Using API refreshToken method");
          try {
            const newToken = await api.refreshToken();
            if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] API refreshToken succeeded, new token:", newToken ? "present" : "missing");

            // Check the state again after refresh to see if refreshToken is still there
            const newState = store.getState();
            if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] State after API refreshToken:", {
              isAuthenticated: newState.auth.isAuthenticated,
              hasToken: !!newState.auth.token,
              hasRefreshToken: !!newState.auth.refreshToken
            });

            return; // If we successfully refreshed the token, we're done
          } catch (refreshError) {
            if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] API refreshToken failed:", refreshError);
            // Fall back to silent reauth
          }
        }

        // Fall back to silent reauth if API refresh failed or not available
        if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Falling back to silent reauth");
        const newToken = await trySilentReauth();
        if (newToken) {
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Silent reauth succeeded, new token:", newToken ? "present" : "missing");

          // Keep the existing user data and refreshToken when refreshing the token
          if (window.YCG_AUTH_ACTIONS) {
            if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Using auth actions for token refresh");
            store.dispatch("auth", window.YCG_AUTH_ACTIONS.loginSuccess(
              state.auth.user,
              newToken,
              state.auth.refreshToken // Preserve the existing refresh token
            ));
          } else {
            if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Auth actions not available, using direct dispatch");
            store.dispatch("auth", {
              type: "LOGIN_SUCCESS",
              payload: {
                user: state.auth.user,
                token: newToken,
                refreshToken: state.auth.refreshToken // Preserve the existing refresh token
              }
            });
          }

          // Check the state after dispatch
          const stateAfterDispatch = store.getState();
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] State after dispatch:", {
            isAuthenticated: stateAfterDispatch.auth.isAuthenticated,
            hasToken: !!stateAfterDispatch.auth.token,
            hasRefreshToken: !!stateAfterDispatch.auth.refreshToken
          });

          await store.saveToStorage();
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] State saved to storage");

          if (window.YCG_UI) window.YCG_UI.updateUI(store.getState());
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] UI updated with new state");

          // Clear any logout marker that might exist
          await clearLogoutMarker();
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Logout marker cleared");
        } else {
          if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Silent reauth returned no token");
        }
      } catch (err) {
        console.warn("[AUTH-DEBUG] Token refresh failed:", err);

        // Don't automatically log out - just let the token expire
        // The user will be prompted to log in again when they try to use the extension
        // This prevents unexpected logouts
        if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Token refresh failed, but not forcing logout");
      }
    } else {
      if (window.YCG_DEBUG) console.log("[AUTH-DEBUG] Token not expiring soon, no refresh needed");
    }
  } catch (error) {
    console.error("[AUTH-DEBUG] Error in token check:", error);
  }
}

// Start monitor on DOMContentLoaded
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", startTokenExpiryMonitor);
} else {
  startTokenExpiryMonitor();
}

// On logout, always stop expiry monitor
window.addEventListener("ycg-logout", () => {
  stopTokenExpiryMonitor();
});

// --- Google Sign-In logic ---
let googleSignInInitialized = false;

/**
 * Initialize Google Sign-In
 */
function initGoogleSignIn() {
  console.log("[Auth] Initializing Google Sign-In...");

  // Create Google Sign-In buttons
  const createGoogleButton = () => {
    const button = document.createElement("button");
    button.type = "button";
    button.innerHTML = `
      <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 18 18\" style=\"margin-right: 8px;\">
        <path fill=\"#4285F4\" d=\"M17.64 9.2c0-.637-.057-1.251-.164-1.84H9v3.481h4.844c-.209 1.125-.843 2.078-1.796 2.717v2.258h2.908c1.702-1.567 2.684-3.874 2.684-6.615z\"/>
        <path fill=\"#34A853\" d=\"M9 18c2.43 0 4.467-.806 5.956-2.18l-2.908-2.259c-.806.54-1.837.86-3.048.86-2.344 0-4.328-1.584-5.036-3.711H.957v2.332A8.997 8.997 0 0 0 9 18z\"/>
        <path fill=\"#FBBC05\" d=\"M3.964 10.71A5.41 5.41 0 0 1 3.682 9c0-.593.102-1.17.282-1.71V4.958H.957A8.996 8.996 0 0 0 0 9c0 1.452.348 2.827.957 4.042l3.007-2.332z\"/>
        <path fill=\"#EA4335\" d=\"M9 3.58c1.321 0 2.508.454 3.44 1.345l2.582-2.58C13.463.891 11.426 0 9 0A8.997 8.997 0 0 0 .957 4.958L3.964 7.29C4.672 5.163 6.656 3.58 9 3.58z\"/>
      </svg>
      Continue with Google
    `;
    button.className = "google-signin-btn-dynamic";
    button.addEventListener("click", handleGoogleSignIn);
    return button;
  };

  // Add buttons to the DOM
  const googleSignInButton1 = document.getElementById("google-signin-button");
  const googleSignInButton2 = document.getElementById("google-signin-button-auth");

  if (googleSignInButton1) {
    googleSignInButton1.innerHTML = "";
    googleSignInButton1.appendChild(createGoogleButton());
  }
  if (googleSignInButton2) {
    googleSignInButton2.innerHTML = "";
    googleSignInButton2.appendChild(createGoogleButton());
  }
}

async function handleGoogleSignIn() {
  // Only trigger OAuth flow on explicit user click
  try {
    // 1. Show notification: Connecting to Google...
    if (window.YCG_UI) {
      window.YCG_UI.showNotification("Connecting to Google...", "info");
    }

    // Get Google auth token
    const googleToken = await launchGoogleSignIn();
    if (!googleToken) {
      console.error("[Auth] No Google token received");
      return;
    }

    // 2. Show notification: Signing in...
    if (window.YCG_UI) {
      window.YCG_UI.showNotification("Signing in...", "info");
    }

    // Get references to store and API
    const store = window.YCG_STORE;
    const api = window.YCG_API;
    if (!api) throw new Error("API service not available");
    if (!store) throw new Error("Store not available");

    // Clear any auth flags
    await clearAuthFlags();

    // Exchange Google token for backend JWT + user info
    const loginResult = await api.loginWithGoogle(googleToken);
    if (!loginResult || !loginResult.access_token) {
      throw new Error("Failed to login with Google: No access token returned");
    }

    // Extract user info, tokens, and credits
    const { access_token, refresh_token, user_id, email, name, picture, credits } = loginResult;

    if (window.YCG_DEBUG) {
      console.log("[AUTH-DEBUG] Login successful, received tokens:", {
        accessToken: access_token ? access_token.substring(0, 10) + "..." : "missing",
        refreshToken: refresh_token ? refresh_token.substring(0, 10) + "..." : "missing",
        userId: user_id,
        hasCredits: credits !== undefined
      });
    }

    // Update auth state
    store.dispatch("auth", {
      type: "LOGIN_SUCCESS",
      payload: {
        user: {
          id: user_id,
          email,
          name,
          picture,
        },
        token: access_token,
        refreshToken: refresh_token,
      },
    });

    // Update credits if available
    if (credits !== undefined) {
      store.dispatch("credits", {
        type: "SET_CREDITS",
        payload: {
          count: credits || 0,
        },
      });
    }

    // Set active view to main
    store.dispatch("ui", {
      type: "SET_ACTIVE_VIEW",
      payload: { view: "main" },
    });

    // Save state to storage immediately after login
    if (window.YCG_DEBUG) console.log("[AUTH] Saving state to storage after login");
    await store.saveToStorage();

    // Verify the state was saved correctly
    const savedState = store.getState();
    if (window.YCG_DEBUG) {
      console.log("[AUTH-DEBUG] State after login:", {
        isAuthenticated: savedState.auth.isAuthenticated,
        hasToken: !!savedState.auth.token,
        hasRefreshToken: !!savedState.auth.refreshToken,
        hasUser: !!savedState.auth.user,
        activeView: savedState.ui.activeView
      });
    }

    // Start token refresh monitoring
    startTokenExpiryMonitor();

    // 3. Show success notification
    if (window.YCG_UI) {
      window.YCG_UI.showNotification("Successfully logged in!", "success");
      window.YCG_UI.updateUI(store.getState());
    }

    // Explicitly check for videos after login
    if (window.YCG_VIDEO) {
      window.YCG_VIDEO.checkForVideo();
    }

    // No need to refresh credit balance after login with loading animation
    // The credits are already set during login from the login response
    // And popup.js will refresh credits silently when the popup is opened
    console.log("[AUTH] Login complete - credits already set from login response");

    console.log("[AUTH] Google Sign-In successful");
  } catch (error) {
    handleAuthError(window.YCG_STORE, error);
  }
}

/**
 * Set up authentication event listeners
 */
function setupAuthEventListeners() {
  // Get references to the store and UI
  const store = window.YCG_STORE;

  // Login button
  const loginBtn = document.getElementById("login-btn");
  if (loginBtn) {
    loginBtn.addEventListener("click", () => {
      store.dispatch("ui", {
        type: "SET_ACTIVE_VIEW",
        payload: { view: "auth" },
      });
    });
  }

  // Logout button
  const logoutBtn = document.getElementById("logout-btn");
  if (logoutBtn) {
    logoutBtn.addEventListener("click", async () => {
      await handleLogout();
    });
  }
}

/**
 * Handle explicit user logout
 */
async function handleLogout() {
  const store = window.YCG_STORE;
  const api = window.YCG_API;

  if (!store) {
    console.error("[Auth] Store not available, cannot logout");
    return;
  }

  try {
    if (window.YCG_DEBUG) console.log("[Auth] Starting logout process");

    // Clear any auth flags
    await clearAuthFlags();

    // Call API logout if available
    if (api && api.logout) {
      try {
        await api.logout();
        if (window.YCG_DEBUG) console.log("[Auth] API logout successful");
      } catch (error) {
        console.warn("[Auth] API logout failed:", error);
        // Continue with local logout even if API logout fails
      }
    }

    // Stop token refresh monitoring
    stopTokenExpiryMonitor();
    if (window.YCG_DEBUG) console.log("[Auth] Token expiry monitor stopped");

    // Revoke Google token
    try {
      if (chrome && chrome.identity) {
        chrome.identity.clearAllCachedAuthTokens(() => {
          if (window.YCG_DEBUG) console.log("[Auth] Google tokens revoked on logout");
        });
      }
    } catch (error) {
      console.warn("[Auth] Error revoking Google tokens:", error);
    }

    // Dispatch logout action to store
    store.dispatch("auth", { type: "LOGOUT" });
    if (window.YCG_DEBUG) console.log("[Auth] Dispatched LOGOUT action");

    // Set active view to welcome
    store.dispatch("ui", {
      type: "SET_ACTIVE_VIEW",
      payload: { view: "welcome" },
    });
    if (window.YCG_DEBUG) console.log("[Auth] Set active view to welcome");

    // Save state to storage immediately
    await store.saveToStorage();
    if (window.YCG_DEBUG) console.log("[Auth] State saved to storage after logout");

    // Verify the state was saved correctly
    const savedState = store.getState();
    if (window.YCG_DEBUG) {
      console.log("[AUTH-DEBUG] State after logout:", {
        isAuthenticated: savedState.auth.isAuthenticated,
        hasToken: !!savedState.auth.token,
        hasRefreshToken: !!savedState.auth.refreshToken,
        hasUser: !!savedState.auth.user,
        activeView: savedState.ui.activeView
      });
    }

    // Show notification
    if (window.YCG_UI) {
      window.YCG_UI.showNotification("Successfully logged out", "success");
      window.YCG_UI.updateUI(store.getState());
    }

    // Dispatch custom event for logout
    window.dispatchEvent(new CustomEvent("ycg-logout"));

    console.log("[Auth] Logout complete");
  } catch (error) {
    console.error("[Auth] Error during logout:", error);

    // Show error notification
    if (window.YCG_UI) {
      window.YCG_UI.showNotification("Error during logout: " + error.message, "error");
    }
  }
}

/**
 * Launch Google Sign-In
 * @returns {Promise<string>} The Google OAuth token
 */
function launchGoogleSignIn() {
  return new Promise((resolve, reject) => {
    try {
      console.log("[Auth] Requesting Google auth token...");

      // Check if chrome is defined
      if (typeof chrome === "undefined" || !chrome.identity) {
        console.error("[Auth] Chrome identity API not available.");
        reject(new Error("Chrome identity API not available."));
        return;
      }

      // Set a timeout for the entire operation
      const timeoutId = setTimeout(() => {
        console.error("[Auth] Google auth token request timed out");
        reject(new Error("Google authentication timed out"));
      }, 15000); // 15 second timeout

      // First try to clear any cached tokens
      chrome.identity.clearAllCachedAuthTokens(() => {
        console.log("[Auth] Cleared cached tokens");

        // Now get a fresh token
        chrome.identity.getAuthToken({ interactive: true }, (token) => {
          // Clear the timeout since we got a response
          clearTimeout(timeoutId);

          if (chrome.runtime.lastError) {
            console.error("[Auth] Chrome identity error:", chrome.runtime.lastError);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (!token) {
            console.error("[Auth] No token returned from Google");
            reject(new Error("Failed to get auth token"));
            return;
          }

          console.log("[Auth] Got Google auth token:", token.substring(0, 10) + "...");
          resolve(token);
        });
      });
    } catch (error) {
      console.error("[Auth] Error in launchGoogleSignIn:", error);
      reject(error);
    }
  });
}

/**
 * Handle authentication error
 * @param {Object} store - The state store
 * @param {Error} error - The error object
 */
function handleAuthError(store, error) {
  const errorMsg = error && error.message ? error.message : String(error);
  const state = store.getState();
  // Only show error if user is authenticated or in the process of logging in
  const shouldShowError = state.auth.isAuthenticated || state.auth.isLoading;
  if (
    errorMsg.includes("Failed to fetch") ||
    errorMsg.includes("network error") ||
    errorMsg.includes("NetworkError")
  ) {
    console.info(`[Auth] Network error or timeout: ${errorMsg}`);
  } else {
    console.error("[Auth] Error getting user info from server, using cached data:", error);
  }

  // Debug: Log state before error handling
  console.log("[AUTH-DEBUG] State before error handling:", {
    auth: store.getState().auth.isAuthenticated,
    view: store.getState().ui.activeView
  });

  // Dispatch login failure action with user-friendly error message
  console.log("[AUTH-DEBUG] Dispatching LOGIN_FAILURE action");
  store.dispatch("auth", {
    type: "LOGIN_FAILURE",
    payload: {
      error: errorMsg,
    },
  });

  // Set active view to welcome
  console.log("[AUTH-DEBUG] Dispatching SET_ACTIVE_VIEW action to show welcome view");
  store.dispatch("ui", {
    type: "SET_ACTIVE_VIEW",
    payload: { view: "welcome" },
  });

  // Debug: Log state after dispatching actions
  console.log("[AUTH-DEBUG] State after dispatching actions:", {
    auth: store.getState().auth.isAuthenticated,
    view: store.getState().ui.activeView
  });

  // Save state to storage
  console.log("[AUTH-DEBUG] Saving state to storage");
  store.saveToStorage();
  console.log("[AUTH-DEBUG] State saved to storage");

  // Debug: Log state after error
  console.log("[AUTH-DEBUG] State after error:", {
    auth: store.getState().auth,
    view: store.getState().ui.activeView
  });

  // Show error notification (user-friendly)
  if (window.YCG_UI) {
    window.YCG_UI.showNotification(errorMsg, "error");
  }

  // Force UI update after error
  console.log("[AUTH-DEBUG] Forcing UI update after error");
  if (window.YCG_UI) {
    window.YCG_UI.updateUI(store.getState());
  }
}

// --- On logout, always revoke Google tokens ---
// (Assume this logic is in your logout handler)
// Example addition for logout handler:
// chrome.identity.clearAllCachedAuthTokens(() => { console.log("[Auth] Google tokens revoked on logout"); });

/**
 * Refresh credit balance from the backend
 * This function is exposed globally so it can be called from popup.js
 */
async function refreshCreditBalance() {
  console.log("[AUTH-CREDITS] Starting credit balance refresh");

  const store = window.YCG_STORE;
  const api = window.YCG_API;

  if (!store || !api) {
    console.log("[AUTH-CREDITS] Store or API not available");
    return false;
  }

  const state = store.getState();
  if (!state.auth || !state.auth.isAuthenticated || !state.auth.token) {
    console.log("[AUTH-CREDITS] User not authenticated, skipping credit refresh");
    return false;
  }

  try {
    console.log("[AUTH-CREDITS] Fetching credit balance");

    // Set loading state
    store.dispatch("credits", { type: "LOADING_CREDITS" });

    // Direct fetch implementation to bypass any potential issues with the API service
    const token = state.auth.token;
    const apiUrl = api.API.CREDITS.BALANCE;
    console.log("[AUTH-CREDITS] Fetching from URL:", apiUrl);

    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json"
      }
    });

    if (!response.ok) {
      console.warn("[AUTH-CREDITS] API response not OK:", response.status);
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    console.log("[AUTH-CREDITS] API response:", data);

    if (data && data.success && data.data && data.data.balance !== undefined) {
      const newBalance = data.data.balance;
      console.log("[AUTH-CREDITS] Credit balance updated to", newBalance);

      // Update the store with the new balance
      store.dispatch("credits", {
        type: "SET_CREDITS",
        payload: { count: newBalance }
      });

      // Save updated state to storage
      await store.saveToStorage();
      console.log("[AUTH-CREDITS] State saved to storage");

      return true;
    } else {
      console.warn("[AUTH-CREDITS] Invalid response format:", data);
      store.dispatch("credits", {
        type: "CREDITS_ERROR",
        payload: { error: "Failed to fetch credit balance" }
      });
      return false;
    }
  } catch (error) {
    console.warn("[AUTH-CREDITS] Error fetching credit balance:", error);
    store.dispatch("credits", {
      type: "CREDITS_ERROR",
      payload: { error: error.message || "Unknown error" }
    });
    return false;
  }
}

// Make the function available globally
// This is important to do early so popup.js can use it
window.YCG_REFRESH_CREDITS = refreshCreditBalance;

// We don't need to refresh credits on every DOM content load
// This will be handled by popup.js when the popup is opened

// Add global DEBUG flag if not present
if (typeof window.YCG_DEBUG === "undefined") window.YCG_DEBUG = false;

console.log = ((origLog) => function(...args) { if (window.YCG_DEBUG) origLog.apply(console, args); })(console.log);
console.info = ((origInfo) => function(...args) { if (window.YCG_DEBUG) origInfo.apply(console, args); })(console.info);
console.debug = ((origDebug) => function(...args) { if (window.YCG_DEBUG) origDebug.apply(console, args); })(console.debug);
