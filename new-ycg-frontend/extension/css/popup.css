:root {
  /* Primary color palette - vibrant purple gradient */
  --primary-from: #6366f1;
  --primary-to: #8b5cf6;
  --primary-hover: #4f46e5;
  --primary-light: #ede9fe;

  /* Secondary colors */
  --secondary: #64748b;
  --success: #10b981;
  --error: #ef4444;
  --warning: #f59e0b;

  /* Background gradients */
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --card-gradient: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  --menu-gradient: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);

  /* Text colors */
  --foreground: #0f172a;
  --muted-foreground: #6b7280;

  /* UI elements */
  --border: #e2e8f0;
  --radius: 8px;
  --radius-lg: 16px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 4px rgba(0, 0, 0, 0.04);
  --shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Animation */
  --transition-fast: 0.15s;
  --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
    "Helvetica Neue", sans-serif;
  color: var(--foreground);
  background: var(--bg-gradient);
  line-height: 1.5;
  font-size: 14px;
}

.app-container {
  width: 450px;
  min-height: 500px;
  max-height: 90vh;
  height: 90vh;
  overflow: hidden;
  position: relative;
  background: var(--bg-gradient);
  box-sizing: border-box;
}

.screen {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: opacity var(--transition), transform var(--transition);
  box-sizing: border-box;
  overflow: hidden;
}

.screen.hidden {
  display: none;
}

/* Welcome Screen */
.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  height: 100%;
  background: var(--menu-gradient);
  border-radius: var(--radius-lg);
  margin: 1rem;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.welcome-content::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
  z-index: -1;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.welcome-logo {
  width: 90px;
  height: 90px;
  margin-bottom: 1rem;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.logo-container h1 {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-message {
  margin-bottom: 2rem;
  color: var(--secondary);
  font-size: 1rem;
  max-width: 280px;
  line-height: 1.6;
}

.auth-footer {
  margin-top: 2rem;
  font-size: 0.75rem;
  color: var(--muted-foreground);
  max-width: 280px;
}

.auth-footer a {
  color: var(--primary-from);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--transition-fast);
}

.auth-footer a:hover {
  color: var(--primary-to);
  text-decoration: underline;
}

/* Google Sign-In Button */
.google-signin-btn-dynamic {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  color: #333;
  border: 1px solid var(--border);
  border-radius: var(--radius-full);
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  box-shadow: var(--shadow);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.google-signin-btn-dynamic::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
}

.google-signin-btn-dynamic:hover {
  background-color: #f9fafb;
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.google-signin-btn-dynamic:hover::before {
  left: 100%;
}

/* Main Header */
.main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
  background: var(--card-gradient);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-left h2 {
  font-size: 1rem;
  font-weight: 600;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.logo {
  width: 48px;
  height: 48px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.header-logo {
  width: 28px;
  height: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Credits Badge */
.credits-badge {
  display: flex;
  align-items: center;
  background: linear-gradient(90deg, var(--primary-from), var(--primary-to));
  background-clip: padding-box;
  -webkit-background-clip: padding-box;
  color: white;
  border-radius: var(--radius-full);
  padding: 0.25rem 0.75rem;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast);
}

.credits-badge:hover {
  transform: scale(1.05);
}

.credits-badge::before {
  content: "✦";
  margin-right: 0.25rem;
  font-size: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* User Profile */
.user-profile {
  cursor: pointer;
  position: relative;
  transition: transform var(--transition-fast);
}

.user-profile:hover {
  transform: scale(1.05);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(to right, var(--primary-from), var(--primary-to)) border-box;
  box-shadow: var(--shadow-sm);
}

/* Settings Button */
.icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.35rem;
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.icon-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, var(--primary-light) 0%, transparent 70%);
  opacity: 0;
  transform: scale(0);
  transition: transform var(--transition-fast), opacity var(--transition-fast);
}

.icon-btn:hover {
  color: var(--primary-from);
}

.icon-btn:hover::before {
  opacity: 1;
  transform: scale(1.5);
}

.icon-btn svg {
  position: relative;
  z-index: 1;
}

/* User Menu */
.user-menu {
  position: absolute;
  top: 60px;
  right: 1rem;
  background: var(--menu-gradient);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
  width: 250px;
  z-index: 20;
  overflow: hidden;
  transform-origin: top right;
  animation: scaleIn 0.2s ease forwards;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.user-menu.hidden {
  display: none;
}

.user-menu-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid var(--border);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.1) 100%);
}

.menu-user-avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(to right, var(--primary-from), var(--primary-to)) border-box;
  box-shadow: var(--shadow-sm);
}

.user-details {
  overflow: hidden;
}

.user-name {
  font-weight: 600;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: var(--foreground);
}

.user-email {
  font-size: 0.75rem;
  color: var(--muted-foreground);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-menu-items {
  padding: 0.5rem 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: var(--foreground);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
}

.menu-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(to right, var(--primary-light), transparent);
  transition: width var(--transition-fast);
}

.menu-item:hover {
  background-color: rgba(99, 102, 241, 0.05);
}

.menu-item:hover::before {
  width: 4px;
}

.menu-item svg {
  color: var(--primary-from);
  transition: transform var(--transition-fast);
}

.menu-item:hover svg {
  transform: scale(1.1);
}

/* Main Content Area */
.main-content-area {
  flex: 1 1 auto;
  height: 100%;
  min-height: 0;
  max-height: none;
  padding: 1rem;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--bg-gradient);
}

/* Video Info */
.video-info {
  background: var(--card-gradient);
  border-radius: var(--radius-lg);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  box-shadow: var(--shadow);
  border: 1px solid rgba(226, 232, 240, 0.7);
  transition: transform var(--transition), box-shadow var(--transition);
}

.video-info:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.video-info h3 {
  font-size: 0.875rem;
  color: var(--primary-from);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.video-title {
  font-weight: 600;
  margin-bottom: 1.25rem;
  word-break: break-word;
  line-height: 1.4;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.625rem 1.25rem;
  cursor: pointer;
  transition: all var(--transition);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  color: white;
  box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
}

.btn-primary:hover {
  box-shadow: 0 6px 10px rgba(99, 102, 241, 0.3);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background-color: var(--muted);
  color: var(--foreground);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background-color: var(--border);
  box-shadow: var(--shadow);
  transform: translateY(-1px);
}

.btn-full {
  width: 100%;
}

/* Loading Animation */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(99, 102, 241, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-from);
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.1);
}

/* Credits badge spinner - smaller version */
.credits-badge .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  margin: 0;
  display: inline-block;
  vertical-align: middle;
  box-shadow: none;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Chapters */
.chapters-container {
  background: var(--card-gradient);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(226, 232, 240, 0.7);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition), box-shadow var(--transition);
}

.chapters-container:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.chapters-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(139, 92, 246, 0.12) 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.7);
}

.chapters-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-from);
  letter-spacing: -0.01em;
}

.version-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  background: white;
  border-radius: var(--radius-full);
  padding: 0.25rem;
  box-shadow: var(--shadow-sm);
}

.version-controls .btn {
  padding: 0.25rem;
  border-radius: var(--radius-full);
  background: transparent;
}

.version-controls .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.version-controls .btn:not(:disabled):hover {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-from);
}

.chapters-content {
  padding: 0;
  max-height: 250px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) transparent;
  background: #fcfcfd;
}

.chapters-content::-webkit-scrollbar {
  width: 6px;
}

.chapters-content::-webkit-scrollbar-track {
  background: transparent;
}

.chapters-content::-webkit-scrollbar-thumb {
  background-color: var(--primary-light);
  border-radius: 20px;
}

.chapters-text {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  margin-bottom: 0;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 0.75rem;
  line-height: 1.4;
  border: none;
  box-shadow: none;
}

/* Style the pre tag to properly format chapters */
.chapters-text pre {
  margin: 0;
  padding: 0;
  white-space: pre-wrap;
  word-break: normal;
  font-family: inherit;
}

/* Create a styled list of chapters */
.chapters-text pre {
  display: block;
}

/* Style each chapter line */
.chapters-text pre {
  padding: 0.75rem 1.25rem;
}

/* Add line separators between chapters */
.chapters-text pre {
  background-image: linear-gradient(to right, transparent, var(--border), transparent);
  background-size: 100% 1px;
  background-position: bottom;
  background-repeat: repeat-x;
}

/* Style the timestamps to stand out */
.chapters-text pre {
  color: var(--foreground);
}

/* Style for timestamps - bold only */
.chapter-timestamp {
  color: var(--primary-from);
  font-weight: 600;
  font-family: "Roboto Mono", monospace;
  margin-right: 0.25rem;
}

.chapter-item {
  display: flex;
  gap: 0.75rem;
  padding: 0.75rem 0.5rem;
  border-bottom: 1px solid var(--border);
  transition: background-color var(--transition-fast);
  border-radius: var(--radius);
}

.chapter-item:hover {
  background-color: rgba(99, 102, 241, 0.05);
}

.chapter-item:last-child {
  border-bottom: none;
}

.chapter-time {
  font-family: "Roboto Mono", monospace;
  color: var(--primary-from);
  font-weight: 500;
  min-width: 60px;
  padding: 0.125rem 0.375rem;
  background: var(--primary-light);
  border-radius: var(--radius);
  text-align: center;
  font-size: 0.75rem;
}

.chapter-title {
  flex: 1;
  line-height: 1.4;
}

.chapters-actions {
  display: flex;
  gap: 0.75rem;
  padding: 1rem;
  border-top: 1px solid var(--border);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.chapters-actions .btn {
  flex: 1;
}

/* Error Message */
.error-message {
  background-color: #fee2e2;
  color: var(--error);
  padding: 0.75rem;
  border-radius: var(--radius);
  margin-bottom: 1rem;
  border-left: 4px solid var(--error);
  animation: slideIn 0.3s ease;
}

.error-message.hidden {
  display: none;
}

/* Status Message */
.status-message {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1.25rem;
  border-radius: var(--radius-lg);
  text-align: center;
  color: var(--muted-foreground);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
  position: relative;
  overflow: hidden;
}

.status-message::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
}

/* Notification */
.notification {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  background-color: var(--foreground);
  color: white;
  box-shadow: var(--shadow-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideIn 0.3s ease;
  z-index: 100;
  border-left: 4px solid transparent;
}

.notification-success {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  border-left-color: #059669;
}

.notification-error {
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  border-left-color: #dc2626;
}

.notification-info {
  background: linear-gradient(135deg, var(--primary-from) 0%, var(--primary-to) 100%);
  border-left-color: var(--primary-from);
}

.notification-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.25rem;
  opacity: 0.7;
  transition: opacity var(--transition-fast);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.notification-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.1);
}

.notification-hide {
  animation: slideOut 0.3s ease forwards;
}

@keyframes slideIn {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

/* Additional utility classes for text */
.text-sm {
  font-size: 0.875rem;
}

.text-muted-foreground {
  color: var(--muted-foreground);
}

.mt-2 {
  margin-top: 0.5rem;
}

/* Fix for the muted background color that was referenced but not defined */
.muted {
  background-color: #f1f5f9;
}

/* Responsive Design */
@media (max-width: 400px) {
  .welcome-content,
  .auth-content,
  .main-content-area {
    padding: 1rem !important;
    margin: 0 !important;
    border-radius: var(--radius);
  }
  .logo-container {
    margin-bottom: 1rem;
  }
  .welcome-logo {
    width: 60px;
    height: 60px;
  }
  .chapters-content {
    max-height: 150px;
  }
  .chapters-actions {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;
  }
  .btn {
    width: 100%;
    min-width: 0;
  }
}

@media (max-width: 340px) {
  .welcome-message, .auth-footer {
    font-size: 0.85rem;
    max-width: 90vw;
  }
}

/* Consistent spacing utilities */
.space-xs { margin: 0.25rem !important; }
.space-sm { margin: 0.5rem !important; }
.space-md { margin: 1rem !important; }
.space-lg { margin: 2rem !important; }

/* Ensure notifications and errors have consistent spacing */
.notification, .error-message, .status-message {
  margin-bottom: 1rem;
  margin-top: 0.5rem;
}

/* Full Size */
.full-size {
  width: 100%;
  height: 100%;
  min-height: 100%;
  min-width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

/* Unified card for main content */
.main-content-card {
  background: var(--card-gradient);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 1.5rem;
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.main-content-card .video-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
}

.main-content-card .chapters-block {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid var(--border);
  transition: box-shadow var(--transition);
  width: 100%;
}

.chapters-block:hover {
  box-shadow: var(--shadow-lg);
}

.main-content-card .actions {
  display: flex;
  gap: 1rem;
  width: 100%;
}

.main-content-card .actions .btn {
  flex: 1;
}

#welcome-container.full-size {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--menu-gradient);
  box-sizing: border-box;
  overflow: hidden;
}

.welcome-content.full-size {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: none;
  box-shadow: none;
  border-radius: 0;
  padding: 0;
  overflow: hidden;
}

/* --- Integrated from redesign/2popup.css for Buy More Credits redesign --- */
.credits-menu {
  padding: 0.75rem;
  background: var(--menu-gradient);
  border-radius: var(--radius-lg);
  max-height: 400px;
  overflow-y: auto;
}

.credits-menu.hidden { display: none; }

.credits-option {
  background: white;
  border-radius: var(--radius);
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.credits-option-header {
  margin-bottom: 0.5rem;
}

.credits-option-header h3 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-from);
}

.subscription-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
}

.toggle-label {
  font-size: 0.875rem;
  color: var(--muted-foreground);
}

.toggle-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.subscription-checkbox {
  position: relative;
  width: 32px;
  height: 18px;
  appearance: none;
  background: #e2e8f0;
  border-radius: 10px;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.subscription-checkbox:checked {
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
}

.subscription-checkbox::before {
  content: "";
  position: absolute;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  background: white;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.subscription-checkbox:checked::before {
  left: 16px;
}

.toggle-status {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--muted-foreground);
}

.purchase-btn {
  width: 100%;
  background: linear-gradient(to right, var(--primary-from), var(--primary-to));
  background-clip: padding-box;
  -webkit-background-clip: padding-box;
  color: white;
  border: none;
  border-radius: var(--radius);
  font-size: 0.85rem;
  font-weight: 600;
  padding: 0.5rem;
  cursor: pointer;
  transition: background var(--transition-fast), box-shadow var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.purchase-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.back-button-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.25rem;
}

.back-button {
  background: none;
  border: none;
  color: var(--primary-from);
  font-size: 0.95rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  cursor: pointer;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius);
  transition: background var(--transition-fast);
}

.back-button:hover {
  background: #f1f5f9;
}

.credits-menu::-webkit-scrollbar {
  width: 4px;
}

.credits-menu::-webkit-scrollbar-track {
  background: transparent;
}

.credits-menu::-webkit-scrollbar-thumb {
  background-color: var(--primary-light);
  border-radius: 20px;
}
