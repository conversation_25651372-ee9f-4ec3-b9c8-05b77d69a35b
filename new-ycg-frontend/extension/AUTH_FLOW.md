# Authentication & Token Refresh Flow

## Overview
This document describes how authentication, refresh tokens, and logout are handled in the YouTube Chapter Generator extension (frontend + backend).

---

## 1. Login
- On successful login (including Google OAuth), the backend returns:
  - `access_token` (JWT, short-lived)
  - `refresh_token` (random, long-lived, revocable)
  - `user_id`, `email`, and other user info
- The extension stores these tokens securely in Chrome local storage as a single object.

---

## 2. Access Token Usage
- The `access_token` is sent in the `Authorization` header for all authenticated API requests.
- The extension automatically checks if the access token is about to expire (within 5 minutes) before each request.

---

## 3. Refresh Token Flow
- If the access token is expiring soon or expired:
  - The extension calls the `/refresh` endpoint, sending `user_id` and `refresh_token`.
  - The backend validates and rotates the refresh token, issuing a new `access_token` and `refresh_token`.
  - The extension updates its local storage and state with the new tokens.
- This flow is transparent to the user—no need to log in again unless the refresh token is revoked or expired.

---

## 4. Logout
- When a user logs out:
  - The extension sends both `user_id` and `refresh_token` (and optionally the Google token) to the `/logout` endpoint.
  - The backend revokes the refresh token in Redis, immediately invalidating it.
  - The extension clears all tokens from local storage and updates the UI to the logged-out state.

---

## 5. Security Best Practices
- **Refresh tokens are stored only in extension local storage, never exposed to web pages.**
- **Tokens are never stored in plaintext in the backend; only hashes are kept in Redis.**
- **If a refresh token is revoked or expired, the user is required to log in again.**

---

## 6. Key Files
- `js/auth/token_storage.js`: Handles secure storage and retrieval of tokens.
- `js/api.js`: Handles API requests, automatic token refresh, and logout logic.
- Backend: `/api/routes/auth.py` and `/api/services/token_service.py` handle refresh and logout endpoints/logic.

---

## 7. Troubleshooting
- If you see repeated login prompts, your refresh token may have expired or been revoked. Log in again to restore access.
- If you encounter errors during refresh, check network connectivity and backend availability.

---

## 8. For Developers
- To test the flow, log in, wait for the access token to expire, and observe that the extension refreshes tokens automatically.
- Logging out should immediately clear all tokens and revoke the session on the backend.

---

For further details or issues, consult the code comments in the files above or contact the project maintainer.
