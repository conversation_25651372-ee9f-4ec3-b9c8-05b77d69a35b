#!/bin/bash
# fresh-build.sh - A build script that creates a completely fresh build with only standard files

# Default to development if no environment is specified
ENV=${1:-development}

# Validate environment
if [[ "$ENV" != "production" && "$ENV" != "development" ]]; then
  echo "Error: Invalid environment. Use 'production' or 'development'."
  echo "Usage: ./fresh-build.sh [environment]"
  exit 1
fi

echo "Building YouTube Chapter Generator extension for $ENV environment (fresh build)..."

# Set directories
BUILD_DIR="build"
DIST_DIR="$BUILD_DIR/dist"
EXTENSION_DIR="extension"
TEMPLATES_DIR="$BUILD_DIR/templates"
ZIP_NAME="youtube-chapter-generator-$ENV.zip"

# Check if template exists
MANIFEST_TEMPLATE="$TEMPLATES_DIR/manifest.$ENV.json"
if [ ! -f "$MANIFEST_TEMPLATE" ]; then
  echo "Error: Manifest template for $ENV not found at $MANIFEST_TEMPLATE"
  exit 1
fi

# Navigate to the project root
cd "$(dirname "$0")"

# Clean and create dist directory
echo "Cleaning dist directory..."
rm -rf "$DIST_DIR"
mkdir -p "$DIST_DIR"

# Remove any existing zip file
echo "Removing existing zip file..."
rm -f "$BUILD_DIR/$ZIP_NAME"

# Create necessary directories
mkdir -p "$DIST_DIR/js"
mkdir -p "$DIST_DIR/js/auth"
mkdir -p "$DIST_DIR/js/state/actions"
mkdir -p "$DIST_DIR/js/state/reducers"
mkdir -p "$DIST_DIR/js/state/middleware"
mkdir -p "$DIST_DIR/css"
mkdir -p "$DIST_DIR/icons"

# Copy the appropriate manifest template
echo "Using manifest template for $ENV environment..."
cp "$MANIFEST_TEMPLATE" "$DIST_DIR/manifest.json"

# Check if config template exists
CONFIG_TEMPLATE="$TEMPLATES_DIR/config.$ENV.js"
if [ ! -f "$CONFIG_TEMPLATE" ]; then
  echo "Warning: Config template for $ENV not found at $CONFIG_TEMPLATE"
  echo "Using default config.js from extension directory"
  cp "$EXTENSION_DIR/js/config.js" "$DIST_DIR/js/config.js"
else
  echo "Using config template for $ENV environment..."
  cp "$CONFIG_TEMPLATE" "$DIST_DIR/js/config.js"
fi

# Copy only the standard files (no bundle files)
echo "Copying standard files..."

# Copy HTML files
cp "$EXTENSION_DIR/popup.html" "$DIST_DIR/"

# Copy CSS files
cp "$EXTENSION_DIR/css/popup.css" "$DIST_DIR/css/"

# Copy icon files
cp "$EXTENSION_DIR/icons/icon48.png" "$DIST_DIR/icons/"
cp "$EXTENSION_DIR/icons/icon128.png" "$DIST_DIR/icons/"

# Copy background.js
cp "$EXTENSION_DIR/background.js" "$DIST_DIR/"

# Copy content.js from js directory
# Note: We're keeping it in the js directory to match the manifest
cp "$EXTENSION_DIR/js/content.js" "$DIST_DIR/js/"

# Copy JS files
cp "$EXTENSION_DIR/js/popup.js" "$DIST_DIR/js/"
cp "$EXTENSION_DIR/js/ui.js" "$DIST_DIR/js/"
cp "$EXTENSION_DIR/js/api.js" "$DIST_DIR/js/"
cp "$EXTENSION_DIR/js/video.js" "$DIST_DIR/js/"
cp "$EXTENSION_DIR/js/state.js" "$DIST_DIR/js/"
cp "$EXTENSION_DIR/js/auth.js" "$DIST_DIR/js/"
cp "$EXTENSION_DIR/js/token_manager.js" "$DIST_DIR/js/"
cp "$EXTENSION_DIR/js/popup_feedback.js" "$DIST_DIR/js/"

# Copy auth files
cp "$EXTENSION_DIR/js/auth/auth_init.js" "$DIST_DIR/js/auth/"
cp "$EXTENSION_DIR/js/auth/auth_ui.js" "$DIST_DIR/js/auth/"
cp "$EXTENSION_DIR/js/auth/google_oauth.js" "$DIST_DIR/js/auth/"
cp "$EXTENSION_DIR/js/auth/token_storage.js" "$DIST_DIR/js/auth/"

# Copy state files
cp "$EXTENSION_DIR/js/state/store.js" "$DIST_DIR/js/state/"
cp "$EXTENSION_DIR/js/state/selectors.js" "$DIST_DIR/js/state/"

# Copy state actions
cp "$EXTENSION_DIR/js/state/actions/auth.js" "$DIST_DIR/js/state/actions/"
cp "$EXTENSION_DIR/js/state/actions/chapters.js" "$DIST_DIR/js/state/actions/"
cp "$EXTENSION_DIR/js/state/actions/credits.js" "$DIST_DIR/js/state/actions/"
cp "$EXTENSION_DIR/js/state/actions/ui.js" "$DIST_DIR/js/state/actions/"
cp "$EXTENSION_DIR/js/state/actions/video.js" "$DIST_DIR/js/state/actions/"

# Copy state reducers
cp "$EXTENSION_DIR/js/state/reducers/auth.js" "$DIST_DIR/js/state/reducers/"
cp "$EXTENSION_DIR/js/state/reducers/chapters.js" "$DIST_DIR/js/state/reducers/"
cp "$EXTENSION_DIR/js/state/reducers/credits.js" "$DIST_DIR/js/state/reducers/"
cp "$EXTENSION_DIR/js/state/reducers/ui.js" "$DIST_DIR/js/state/reducers/"
cp "$EXTENSION_DIR/js/state/reducers/video.js" "$DIST_DIR/js/state/reducers/"

# Copy state middleware
cp "$EXTENSION_DIR/js/state/middleware/storage.js" "$DIST_DIR/js/state/middleware/"

# Create a completely new zip file
echo "Creating fresh $ZIP_NAME..."
cd "$DIST_DIR"
# Use -j to store without directory structure
zip -r "../$ZIP_NAME" * -x "*.DS_Store"
cd ../..

echo "Build complete! The extension is ready in $DIST_DIR/ and the zip file is in the $BUILD_DIR directory"
echo ""
echo "To load the extension in Chrome:"
echo "1. Go to chrome://extensions/"
echo "2. Enable 'Developer mode'"
echo "3. Click 'Load unpacked' and select the $DIST_DIR directory"
