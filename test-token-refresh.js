/**
 * Test script for debugging token refresh issues
 * 
 * This script tests the token refresh flow in the YouTube Chapter Generator extension
 * to diagnose the "Invalid refresh token" error.
 * 
 * To use:
 * 1. Open the extension popup
 * 2. Open Chrome DevTools (right-click > Inspect)
 * 3. Paste this script in the console and run it
 * 4. Check the console logs for detailed information about the token refresh process
 */

(async function() {
  console.log("=== TOKEN REFRESH TEST SCRIPT ===");
  console.log("Starting token refresh test...");

  // Check if we're in the extension context
  if (typeof chrome === 'undefined' || !chrome.runtime) {
    console.error("This script must be run in the extension context");
    return;
  }

  // Check if the API service is available
  if (!window.YCG_API) {
    console.error("API service not available. Make sure you're running this in the extension popup.");
    return;
  }

  // Check if the store is available
  if (!window.YCG_STORE) {
    console.error("Store not available. Make sure you're running this in the extension popup.");
    return;
  }

  // Get the current auth state
  const state = window.YCG_STORE.getState();
  const authState = state.auth;

  console.log("Current auth state:", {
    isAuthenticated: authState.isAuthenticated,
    hasToken: !!authState.token,
    hasRefreshToken: !!authState.refreshToken,
    hasUser: !!authState.user,
    userId: authState.user ? authState.user.id : null,
    tokenLength: authState.token ? authState.token.length : 0,
    refreshTokenLength: authState.refreshToken ? authState.refreshToken.length : 0
  });

  if (!authState.isAuthenticated) {
    console.error("User is not authenticated. Please log in first.");
    return;
  }

  if (!authState.token) {
    console.error("No access token found in store.");
    return;
  }

  if (!authState.refreshToken) {
    console.error("No refresh token found in store.");
    return;
  }

  if (!authState.user || !authState.user.id) {
    console.error("No user ID found in store.");
    return;
  }

  // Test 1: Parse the current token to check its expiration
  console.log("\n=== TEST 1: TOKEN EXPIRATION CHECK ===");
  try {
    const tokenParts = authState.token.split('.');
    if (tokenParts.length !== 3) {
      console.error("Invalid token format (not a valid JWT)");
    } else {
      const payload = JSON.parse(atob(tokenParts[1]));
      console.log("Token payload:", payload);
      
      if (payload.exp) {
        const expirationTime = new Date(payload.exp * 1000);
        const currentTime = new Date();
        const timeUntilExpiration = expirationTime - currentTime;
        
        console.log("Token expiration:", {
          expirationTime: expirationTime.toISOString(),
          currentTime: currentTime.toISOString(),
          timeUntilExpiration: Math.floor(timeUntilExpiration / 1000) + " seconds",
          isExpired: expirationTime < currentTime
        });
      } else {
        console.error("Token does not have an expiration claim");
      }
    }
  } catch (error) {
    console.error("Error parsing token:", error);
  }

  // Test 2: Check if the refresh token is valid by making a direct API call
  console.log("\n=== TEST 2: REFRESH TOKEN VALIDATION ===");
  try {
    const refreshEndpoint = "https://new-ycg.vercel.app/v1/auth/refresh";
    console.log("Making direct API call to refresh endpoint:", refreshEndpoint);
    
    const response = await fetch(refreshEndpoint, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        user_id: authState.user.id,
        refresh_token: authState.refreshToken,
      }),
    });
    
    console.log("Refresh token response status:", response.status);
    
    const data = await response.json();
    console.log("Refresh token response:", {
      success: data.success,
      error: data.error,
      hasAccessToken: data.data && !!data.data.access_token,
      hasRefreshToken: data.data && !!data.data.refresh_token
    });
    
    if (!response.ok || !data.success) {
      console.error("Refresh token validation failed:", data.error || "Unknown error");
    } else {
      console.log("Refresh token is valid!");
    }
  } catch (error) {
    console.error("Error validating refresh token:", error);
  }

  // Test 3: Try to get a Google token silently
  console.log("\n=== TEST 3: GOOGLE TOKEN SILENT RETRIEVAL ===");
  try {
    console.log("Attempting to get Google token silently...");
    
    await new Promise((resolve, reject) => {
      if (!chrome.identity) {
        console.error("chrome.identity API not available");
        reject(new Error("chrome.identity not available"));
        return;
      }
      
      // Clear cached tokens first
      chrome.identity.clearAllCachedAuthTokens(() => {
        console.log("Cleared cached Google tokens");
        
        // Get a new token without user interaction
        chrome.identity.getAuthToken({ interactive: false }, (token) => {
          if (chrome.runtime.lastError || !token) {
            const error = chrome.runtime.lastError ? 
              chrome.runtime.lastError.message : 
              "No token returned";
            
            console.error("Silent Google auth failed:", error);
            reject(new Error(`Silent Google auth failed: ${error}`));
            return;
          }
          
          console.log("Successfully got Google token silently:", token.substring(0, 10) + "...");
          resolve(token);
        });
      });
    });
    
    console.log("Google token retrieval test passed!");
  } catch (error) {
    console.error("Error getting Google token silently:", error);
  }

  // Test 4: Try the API's refreshToken method
  console.log("\n=== TEST 4: API REFRESH TOKEN METHOD ===");
  try {
    console.log("Calling API.refreshToken() method...");
    const newToken = await window.YCG_API.refreshToken();
    
    if (newToken) {
      console.log("Token refresh successful! New token:", newToken.substring(0, 10) + "...");
    } else {
      console.error("Token refresh failed: No token returned");
    }
  } catch (error) {
    console.error("Error refreshing token via API method:", error);
  }

  console.log("\n=== TOKEN REFRESH TEST COMPLETE ===");
})();
