#!/bin/bash
# fixed-build.sh - <PERSON><PERSON><PERSON> to build the extension with fixed webpack config

# Default to development if no environment is specified
ENV=${1:-development}

# Validate environment
if [[ "$ENV" != "production" && "$ENV" != "development" ]]; then
  echo "Error: Invalid environment. Use 'production' or 'development'."
  echo "Usage: ./fixed-build.sh [environment]"
  exit 1
fi

echo "Building YouTube Chapter Generator extension for $ENV environment (fixed build)..."

# Set directories
BUILD_DIR="build"
DIST_DIR="$BUILD_DIR/dist"
EXTENSION_DIR="extension"
TEMPLATES_DIR="$BUILD_DIR/templates"

# Check if template exists
MANIFEST_TEMPLATE="$TEMPLATES_DIR/manifest.$ENV.json"
if [ ! -f "$MANIFEST_TEMPLATE" ]; then
  echo "Error: Manifest template for $ENV not found at $MANIFEST_TEMPLATE"
  exit 1
fi

# Navigate to the project root
cd "$(dirname "$0")"

# Check if node_modules exists in build directory
if [ ! -d "$BUILD_DIR/node_modules" ]; then
  echo "Installing dependencies..."
  cd "$BUILD_DIR"
  npm install
  cd ..
fi

# Clean and create dist directory
echo "Cleaning dist directory..."
rm -rf "$DIST_DIR"
mkdir -p "$DIST_DIR"

# Copy the appropriate manifest and config templates
echo "Using manifest and config templates for $ENV environment..."
cp "$MANIFEST_TEMPLATE" "$DIST_DIR/manifest.json"

# Check if config template exists
CONFIG_TEMPLATE="$TEMPLATES_DIR/config.$ENV.js"
if [ ! -f "$CONFIG_TEMPLATE" ]; then
  echo "Warning: Config template for $ENV not found at $CONFIG_TEMPLATE"
  echo "Using default config.js from extension directory"
  mkdir -p "$DIST_DIR/js"
  cp "$EXTENSION_DIR/js/config.js" "$DIST_DIR/js/config.js"
else
  echo "Using config template for $ENV environment..."
  mkdir -p "$DIST_DIR/js"
  cp "$CONFIG_TEMPLATE" "$DIST_DIR/js/config.js"
fi

# Build using webpack with our fixed config
echo "Building extension with fixed webpack config..."
NODE_PATH="$PWD/$BUILD_DIR/node_modules" npx webpack --config fixed-webpack.config.js

# Make sure the config.js file is copied after webpack build
echo "Re-copying config.js template..."
if [ -f "$CONFIG_TEMPLATE" ]; then
  mkdir -p "$DIST_DIR/js"
  cp "$CONFIG_TEMPLATE" "$DIST_DIR/js/config.js"
fi

# Create a zip file for Chrome Web Store submission
ZIP_NAME="youtube-chapter-generator-$ENV.zip"
echo "Creating $ZIP_NAME..."
cd "$DIST_DIR"
zip -r "../$ZIP_NAME" *
cd ../..

echo "Build complete! The extension is ready in $DIST_DIR/ and the zip file is in the $BUILD_DIR directory"
echo ""
echo "To load the extension in Chrome:"
echo "1. Go to chrome://extensions/"
echo "2. Enable 'Developer mode'"
echo "3. Click 'Load unpacked' and select the $DIST_DIR directory"
