/**
 * Test script for debugging token storage issues
 * 
 * This script examines how tokens are stored in Chrome's local storage
 * to help diagnose the "Invalid refresh token" error.
 * 
 * To use:
 * 1. Open the extension popup
 * 2. Open Chrome DevTools (right-click > Inspect)
 * 3. Paste this script in the console and run it
 * 4. Check the console logs for detailed information about token storage
 */

(async function() {
  console.log("=== TOKEN STORAGE TEST SCRIPT ===");
  console.log("Examining token storage...");

  // Check if we're in the extension context
  if (typeof chrome === 'undefined' || !chrome.storage || !chrome.storage.local) {
    console.error("This script must be run in the extension context with storage permission");
    return;
  }

  // Test 1: Check what's in chrome.storage.local
  console.log("\n=== TEST 1: CHROME.STORAGE.LOCAL CONTENTS ===");
  try {
    const storageData = await new Promise((resolve) => {
      chrome.storage.local.get(null, (data) => {
        resolve(data);
      });
    });
    
    console.log("All storage data keys:", Object.keys(storageData));
    
    // Look for token-related keys
    const tokenKeys = Object.keys(storageData).filter(key => 
      key.includes('token') || key.includes('auth') || key.includes('ycg')
    );
    
    console.log("Token-related keys:", tokenKeys);
    
    // Check for the legacy token storage
    if (storageData.ycg_token) {
      console.log("Found legacy token storage (ycg_token):", {
        hasAccessToken: !!storageData.ycg_token.access_token,
        hasRefreshToken: !!storageData.ycg_token.refresh_token,
        accessTokenLength: storageData.ycg_token.access_token ? storageData.ycg_token.access_token.length : 0,
        refreshTokenLength: storageData.ycg_token.refresh_token ? storageData.ycg_token.refresh_token.length : 0,
        userId: storageData.ycg_token.user_id
      });
    } else {
      console.log("No legacy token storage found (ycg_token)");
    }
    
    // Check for the store state
    if (storageData.ycg_store_state) {
      console.log("Found store state (ycg_store_state)");
      
      try {
        const state = JSON.parse(storageData.ycg_store_state);
        console.log("Store state structure:", Object.keys(state));
        
        if (state.auth) {
          console.log("Auth state in storage:", {
            isAuthenticated: state.auth.isAuthenticated,
            hasToken: !!state.auth.token,
            hasRefreshToken: !!state.auth.refreshToken,
            hasUser: !!state.auth.user,
            userId: state.auth.user ? state.auth.user.id : null,
            tokenLength: state.auth.token ? state.auth.token.length : 0,
            refreshTokenLength: state.auth.refreshToken ? state.auth.refreshToken.length : 0
          });
        } else {
          console.log("No auth state found in store state");
        }
      } catch (error) {
        console.error("Error parsing store state:", error);
      }
    } else {
      console.log("No store state found (ycg_store_state)");
    }
  } catch (error) {
    console.error("Error accessing chrome.storage.local:", error);
  }

  // Test 2: Compare storage with current state in memory
  console.log("\n=== TEST 2: COMPARING STORAGE WITH MEMORY STATE ===");
  if (window.YCG_STORE) {
    const memoryState = window.YCG_STORE.getState();
    console.log("Current state in memory:", {
      hasAuth: !!memoryState.auth,
      isAuthenticated: memoryState.auth ? memoryState.auth.isAuthenticated : false,
      hasToken: memoryState.auth && !!memoryState.auth.token,
      hasRefreshToken: memoryState.auth && !!memoryState.auth.refreshToken,
      tokenLength: memoryState.auth && memoryState.auth.token ? memoryState.auth.token.length : 0,
      refreshTokenLength: memoryState.auth && memoryState.auth.refreshToken ? memoryState.auth.refreshToken.length : 0
    });
    
    // Get the storage state for comparison
    const storageData = await new Promise((resolve) => {
      chrome.storage.local.get('ycg_store_state', (data) => {
        resolve(data);
      });
    });
    
    if (storageData.ycg_store_state) {
      try {
        const storageState = JSON.parse(storageData.ycg_store_state);
        
        // Compare auth state
        if (memoryState.auth && storageState.auth) {
          const memoryAuth = memoryState.auth;
          const storageAuth = storageState.auth;
          
          console.log("Auth state comparison (memory vs storage):", {
            isAuthenticated: {
              memory: memoryAuth.isAuthenticated,
              storage: storageAuth.isAuthenticated,
              match: memoryAuth.isAuthenticated === storageAuth.isAuthenticated
            },
            tokenMatch: memoryAuth.token === storageAuth.token,
            refreshTokenMatch: memoryAuth.refreshToken === storageAuth.refreshToken,
            userIdMatch: memoryAuth.user && storageAuth.user ? 
              memoryAuth.user.id === storageAuth.user.id : 
              'User object not available in one or both states'
          });
        } else {
          console.log("Cannot compare auth states - missing in memory or storage");
        }
      } catch (error) {
        console.error("Error parsing storage state for comparison:", error);
      }
    } else {
      console.log("No store state in storage to compare with");
    }
  } else {
    console.log("YCG_STORE not available, cannot compare with memory state");
  }

  // Test 3: Check token storage methods
  console.log("\n=== TEST 3: TOKEN STORAGE METHODS ===");
  if (window.YCG_API) {
    console.log("API service available, checking token methods");
    
    // Check if the API has token-related methods
    console.log("API token methods available:", {
      hasGetToken: typeof window.YCG_API.getToken === 'function',
      hasRefreshToken: typeof window.YCG_API.refreshToken === 'function',
      hasLogout: typeof window.YCG_API.logout === 'function'
    });
    
    // Check the current token if getToken is available
    if (typeof window.YCG_API.getToken === 'function') {
      const token = window.YCG_API.getToken();
      console.log("Current token from API.getToken():", token ? `${token.substring(0, 10)}...` : 'No token');
    }
  } else {
    console.log("API service not available");
  }

  console.log("\n=== TOKEN STORAGE TEST COMPLETE ===");
})();
