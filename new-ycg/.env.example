# OpenAI API key
OPENAI_API_KEY=your_openai_api_key_here

# Webshare proxy credentials (for avoiding IP blocks)
WEBSHARE_USERNAME=your_webshare_username
WEBSHARE_PASSWORD=your_webshare_password

# Authentication
JWT_SECRET_KEY=your_jwt_secret_key_here

# Database
REDIS_URL=your_redis_url_here

# Payment processing
STRIPE_API_KEY=your_stripe_api_key_here
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
FRONTEND_URL=http://localhost:3000