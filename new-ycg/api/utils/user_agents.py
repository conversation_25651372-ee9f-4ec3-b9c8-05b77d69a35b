"""
User Agent rotation utility for YouTube transcript fetching
Provides realistic user agents and browser-specific headers to avoid detection
"""
import random
from typing import Dict, List, Tuple, Any


class UserAgentRotator:
    """Manages user agent rotation with browser-specific headers"""

    # Comprehensive list of realistic user agents
    USER_AGENTS = [
        # Chrome on Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",

        # Chrome on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_6_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",

        # Chrome on Linux
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",

        # Firefox on Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0",
        "Mozilla/5.0 (Windows NT 11.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",

        # Firefox on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:119.0) Gecko/20100101 Firefox/119.0",

        # Firefox on Linux
        "Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0",

        # Safari on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 13_6_1) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",

        # Edge on Windows
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",

        # Edge on macOS
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",

        # Mobile Chrome on Android
        "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 13; SM-A536B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",

        # Mobile Safari on iOS (Expanded for better rotation)
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 16_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 16_7_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",

        # Opera
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/*********",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 OPR/*********",

        # Additional Chrome variants
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    ]

    def __init__(self):
        """Initialize the user agent rotator"""
        self._current_index = 0
        self._shuffled_agents = self.USER_AGENTS.copy()
        random.shuffle(self._shuffled_agents)

        # Separate iOS agents for prioritized access
        self._ios_agents = [ua for ua in self.USER_AGENTS if "iPhone" in ua or "iPad" in ua]
        self._ios_index = 0
        random.shuffle(self._ios_agents)

    def get_next_agent(self) -> str:
        """Get the next user agent in rotation"""
        agent = self._shuffled_agents[self._current_index]
        self._current_index = (self._current_index + 1) % len(self._shuffled_agents)

        # Re-shuffle when we complete a full cycle
        if self._current_index == 0:
            random.shuffle(self._shuffled_agents)

        return agent

    def get_random_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.USER_AGENTS)

    def get_ios_agent(self) -> str:
        """Get the next iOS user agent in rotation"""
        if not self._ios_agents:
            # Fallback to regular agent if no iOS agents available
            return self.get_next_agent()

        agent = self._ios_agents[self._ios_index]
        self._ios_index = (self._ios_index + 1) % len(self._ios_agents)

        # Re-shuffle when we complete a full cycle
        if self._ios_index == 0:
            random.shuffle(self._ios_agents)

        return agent

    def get_random_ios_agent(self) -> str:
        """Get a random iOS user agent"""
        if not self._ios_agents:
            return self.get_random_agent()
        return random.choice(self._ios_agents)

    def get_browser_headers(self, user_agent: str) -> Dict[str, str]:
        """Generate browser-specific headers based on user agent"""
        headers = {
            "User-Agent": user_agent,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Accept-Encoding": "gzip, deflate, br",
            "DNT": "1",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cache-Control": "max-age=0",
            "sec-fetch-dest": "document",
            "sec-fetch-mode": "navigate",
            "sec-fetch-site": "none",
            "sec-fetch-user": "?1",
        }

        # Browser-specific headers
        if "Chrome" in user_agent and "Edg" not in user_agent:
            # Chrome-specific headers
            headers.update({
                "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
                "sec-ch-ua-mobile": "?1" if "Mobile" in user_agent else "?0",
                "sec-ch-ua-platform": self._get_platform_from_ua(user_agent),
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
            })
        elif "Firefox" in user_agent:
            # Firefox doesn't use sec-ch-ua headers
            headers.update({
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
            })
        elif "Safari" in user_agent and "Chrome" not in user_agent:
            # Safari-specific headers (iOS/macOS)
            if "iPhone" in user_agent or "iPad" in user_agent:
                # iOS Safari headers
                headers.update({
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.9",
                    "Accept-Encoding": "gzip, deflate",
                })
                # Remove desktop-specific headers for iOS
                headers.pop("sec-fetch-dest", None)
                headers.pop("sec-fetch-mode", None)
                headers.pop("sec-fetch-site", None)
                headers.pop("sec-fetch-user", None)
            else:
                # macOS Safari headers
                headers.update({
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                })
        elif "Edg" in user_agent:
            # Edge-specific headers
            headers.update({
                "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
                "sec-ch-ua-mobile": "?1" if "Mobile" in user_agent else "?0",
                "sec-ch-ua-platform": self._get_platform_from_ua(user_agent),
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "none",
                "Sec-Fetch-User": "?1",
            })

        return headers

    def _get_platform_from_ua(self, user_agent: str) -> str:
        """Extract platform information from user agent"""
        if "Windows" in user_agent:
            return '"Windows"'
        elif "Macintosh" in user_agent or "Mac OS X" in user_agent:
            return '"macOS"'
        elif "Linux" in user_agent:
            return '"Linux"'
        elif "Android" in user_agent:
            return '"Android"'
        elif "iPhone" in user_agent or "iPad" in user_agent:
            return '"iOS"'
        else:
            return '"Unknown"'

    def get_agent_and_headers(self, rotation_type: str = "sequential") -> Tuple[str, Dict[str, str]]:
        """
        Get user agent and corresponding headers

        Args:
            rotation_type: "sequential", "random", "ios", or "ios_random"

        Returns:
            Tuple of (user_agent, headers_dict)
        """
        if rotation_type == "random":
            user_agent = self.get_random_agent()
        elif rotation_type == "ios":
            user_agent = self.get_ios_agent()
        elif rotation_type == "ios_random":
            user_agent = self.get_random_ios_agent()
        else:
            user_agent = self.get_next_agent()

        headers = self.get_browser_headers(user_agent)
        return user_agent, headers


# Global instance for the application
_rotator_instance = None

def get_rotator() -> UserAgentRotator:
    """Get the global user agent rotator instance"""
    global _rotator_instance
    if _rotator_instance is None:
        _rotator_instance = UserAgentRotator()
    return _rotator_instance

def get_next_user_agent_and_headers() -> Tuple[str, Dict[str, str]]:
    """Convenience function to get next user agent and headers based on config"""
    try:
        from api.config import Config
        rotation_type = Config.USER_AGENT_ROTATION_TYPE if Config.ENABLE_USER_AGENT_ROTATION else "sequential"
        return get_rotator().get_agent_and_headers(rotation_type)
    except ImportError:
        # Fallback if config is not available
        return get_rotator().get_agent_and_headers("sequential")

def get_random_user_agent_and_headers() -> Tuple[str, Dict[str, str]]:
    """Convenience function to get random user agent and headers"""
    return get_rotator().get_agent_and_headers("random")

def get_ios_user_agent_and_headers() -> Tuple[str, Dict[str, str]]:
    """Convenience function to get iOS user agent and headers"""
    return get_rotator().get_agent_and_headers("ios")

def get_random_ios_user_agent_and_headers() -> Tuple[str, Dict[str, str]]:
    """Convenience function to get random iOS user agent and headers"""
    return get_rotator().get_agent_and_headers("ios_random")
