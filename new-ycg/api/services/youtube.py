"""
YouTube transcript fetching services
"""
import json
import time
import traceback
import async<PERSON>
import httpx
import requests
import os
from typing import List, Dict, Any, Optional, Callable

from youtube_transcript_api import (
    YouTubeTranscriptApi,
    TranscriptsDisabled,
    NoTranscriptFound,
    VideoUnavailable,
    RequestBlocked,
    AgeRestricted,
    VideoUnplayable
)
from youtube_transcript_api.proxies import GenericProxyConfig

from api.config import Config

# Decodo proxy config does not require SSL CA patching or special logic

def debug_xml_parsing_error(video_id: str, error_msg: str, attempt: int, proxy_config: Optional[Dict[str, str]] = None):
    """
    Enhanced debugging for XML parsing errors
    """
    debug_info = {
        'video_id': video_id,
        'attempt': attempt + 1,
        'error_message': error_msg,
        'proxy_enabled': bool(proxy_config),
        'timestamp': time.time()
    }

    # Try to get additional info about the video
    try:
        # Test if we can access the video page directly
        test_url = f"https://www.youtube.com/watch?v={video_id}"
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        }

        if proxy_config:
            resp = requests.get(test_url, headers=headers, proxies=proxy_config, timeout=10)
        else:
            resp = requests.get(test_url, headers=headers, timeout=10)

        debug_info['video_page_status'] = resp.status_code
        debug_info['video_page_accessible'] = resp.status_code == 200

        # Check if the page contains transcript-related content
        if resp.status_code == 200:
            page_content = resp.text.lower()
            debug_info['has_transcript_data'] = 'transcript' in page_content
            debug_info['has_captions_data'] = 'captions' in page_content
            debug_info['page_size_bytes'] = len(resp.content)

    except Exception as e:
        debug_info['video_page_error'] = str(e)

    print(f"[DEBUG] XML Parsing Error Details: {debug_info}")

    # Save debug info to file for analysis
    debug_filename = f"xml_debug_{video_id}_{attempt+1}_{int(time.time())}.json"
    try:
        import json
        with open(debug_filename, 'w') as f:
            json.dump(debug_info, f, indent=2)
        print(f"[DEBUG] Saved debug info to {debug_filename}")
    except Exception as e:
        print(f"[DEBUG] Failed to save debug info: {e}")

    return debug_info

def fetch_transcript(video_id: str, timeout_limit: int = 30) -> Optional[List[Dict[str, Any]]]:
    """
    Fetch transcript using the YouTube Transcript API with proper error handling
    and fallbacks. Uses Decodo proxy if configured.

    Args:
        video_id: YouTube video ID
        timeout_limit: Maximum time in seconds to spend fetching the transcript

    Returns:
        List of transcript entries or None if failed
    """
    start_time = time.time()

    # Function to check if we still have time
    def time_left() -> bool:
        """Check if we still have time to continue operations"""
        elapsed = time.time() - start_time
        return elapsed < timeout_limit

    print(f"Fetching transcript for {video_id}, timeout limit: {timeout_limit}s")

    # Try with proxy first, then without
    attempts = [
        ("with Decodo proxy", True),
        ("without proxy", False)
    ]

    for attempt_name, use_proxy in attempts:
        if not time_left():
            print(f"Time limit reached during {attempt_name} attempt")
            break

        print(f"Attempting to fetch transcript {attempt_name}")
        try:
            transcript_list = None
            if use_proxy and Config.get_proxy_dict():
                proxy_config = Config.get_proxy_dict()
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "*/*",
                }
                proxies = proxy_config
                url = f"https://www.youtube.com/watch?v={video_id}"
                from requests.exceptions import ChunkedEncodingError

                # Retry logic specifically for transcript fetching with XML parsing error handling
                for attempt in range(3):
                    try:
                        # Test proxy connectivity first
                        resp = requests.get(url, headers=headers, proxies=proxies, timeout=30, stream=False)
                        if resp.status_code != 200:
                            print(f"Proxy connectivity test failed: status={resp.status_code}")
                            if attempt < 2:
                                time.sleep(2 + attempt)
                                continue
                            else:
                                break

                        print(f"Proxy connectivity test passed (attempt {attempt+1})")

                        # Now attempt to fetch the transcript using new API
                        try:
                            # Create proxy config for new API
                            proxy_config_obj = GenericProxyConfig(
                                http_url=Config.get_proxy_url(),
                                https_url=Config.get_proxy_url()
                            )
                            ytt_api = YouTubeTranscriptApi(proxy_config=proxy_config_obj)
                            fetched_transcript = ytt_api.fetch(video_id, languages=Config.TRANSCRIPT_LANGUAGES)
                            transcript_list = fetched_transcript.to_raw_data()
                            print(f"Successfully fetched transcript on attempt {attempt+1}")
                            break

                        except Exception as transcript_error:
                            error_msg = str(transcript_error)

                            # Check for intermittent XML parsing errors
                            if ("ParseError" in error_msg or "no element found" in error_msg or
                                "xml.parsers.expat.ExpatError" in error_msg or "not well-formed" in error_msg):

                                print(f"XML parsing error on attempt {attempt+1}: {error_msg}")

                                # Enhanced debugging for XML parsing errors
                                debug_info = debug_xml_parsing_error(video_id, error_msg, attempt, proxy_config)

                                if attempt < 2:
                                    wait_time = 3 + (attempt * 2)  # Progressive backoff: 3s, 5s
                                    print(f"Will retry in {wait_time}s due to XML parsing error...")
                                    time.sleep(wait_time)
                                    continue
                                else:
                                    print("Max retries reached for XML parsing error")
                                    print(f"Final debug info: {debug_info}")
                                    # Re-raise the error to be caught by outer exception handler
                                    raise transcript_error
                            else:
                                # For non-XML errors, re-raise immediately
                                print(f"Non-XML transcript error: {error_msg}")
                                raise transcript_error

                    except ChunkedEncodingError as e:
                        print(f"Attempt {attempt+1}: ChunkedEncodingError - {e}")
                        if attempt < 2:
                            time.sleep(2 + attempt)
                            continue
                        else:
                            print("Max retries reached for ChunkedEncodingError")
                            raise e
                    except Exception as e:
                        print(f"Attempt {attempt+1}: Unexpected error - {e}")
                        if attempt < 2:
                            time.sleep(2 + attempt)
                            continue
                        else:
                            raise e
            else:
                # Use new API without proxy
                ytt_api = YouTubeTranscriptApi()
                fetched_transcript = ytt_api.fetch(video_id, languages=Config.TRANSCRIPT_LANGUAGES)
                transcript_list = fetched_transcript.to_raw_data()

            if transcript_list:
                print(f"Successfully fetched transcript {attempt_name} for {video_id}")
                return transcript_list

        except (TranscriptsDisabled, NoTranscriptFound, VideoUnavailable) as e:
            print(f"Transcript not available for {video_id}: {type(e).__name__}: {e}")
            # These are permanent failures, no retry needed
            return None

        except (RequestBlocked, AgeRestricted, VideoUnplayable) as e:
            print(f"Access error for {video_id}: {type(e).__name__}: {e}")
            # Continue to the next attempt
            continue

        except Exception as e:
            print(f"Unexpected error {attempt_name}: {type(e).__name__}: {e}")
            import traceback
            traceback.print_exc()
            # Continue to the next attempt
            continue

    # If we get here, try the httpx-based approach as a last resort
    if time_left():
        try:
            print("Trying httpx-based transcript fetching as fallback")
            proxy_dict = Config.get_proxy_dict() if Config.get_proxy_dict() else None
            transcript_data = fetch_transcript_with_requests(video_id, proxy_dict=proxy_dict)
            return transcript_data
        except Exception as e:
            print(f"httpx-based fallback also failed: {e}")

    print(f"Failed to fetch transcript for {video_id} after all attempts")
    return None


def fetch_transcript_with_requests(video_id: str, proxy_dict: Optional[Dict[str, str]] = None, timeout: int = 10) -> List[Dict[str, Any]]:
    """
    Fetch YouTube transcript using httpx.AsyncClient with Decodo proxy support (async replacement for requests)
    """
    async def _fetch():
        print(f"Attempting to fetch transcript for {video_id} using httpx with Decodo proxy: {bool(proxy_dict)}")
        proxies = proxy_dict if proxy_dict else None
        video_url = f"https://www.youtube.com/watch?v={video_id}"
        async with httpx.AsyncClient(proxies=proxies, timeout=timeout) as client:
            print(f"Fetching video page with Decodo proxy: {bool(proxy_dict)}")
            response = await client.get(video_url)
            response.raise_for_status()
            html = response.text
            start_marker = 'ytInitialPlayerResponse = '
            end_marker = '};'
            start_idx = html.find(start_marker)
            if start_idx == -1:
                raise Exception("Could not find player response in page")
            start_idx += len(start_marker)
            end_idx = html.find(end_marker, start_idx) + 1
            player_response_json = html[start_idx:end_idx]
            player_response = json.loads(player_response_json)
            captions_data = player_response.get('captions', {}).get('playerCaptionsTracklistRenderer', {}).get('captionTracks', [])
            if not captions_data:
                raise Exception("No captions found for this video")
            caption_url = None
            for track in captions_data:
                if track.get('languageCode') in Config.TRANSCRIPT_LANGUAGES:
                    caption_url = track.get('baseUrl')
                    break
            if not caption_url and captions_data:
                caption_url = captions_data[0].get('baseUrl')
            if not caption_url:
                raise Exception("No valid caption URL found")
            caption_url += "&fmt=json3"
            print(f"Fetching captions from {caption_url}")
            captions_response = await client.get(caption_url)
            captions_response.raise_for_status()
            captions_data = captions_response.json()
            transcript = []
            for event in captions_data.get('events', []):
                if 'segs' not in event or 'tStartMs' not in event:
                    continue
                text = ''.join(seg.get('utf8', '') for seg in event.get('segs', []))
                if not text.strip():
                    continue
                transcript.append({
                    'text': text.strip(),
                    'start': event.get('tStartMs') / 1000,
                    'duration': event.get('dDurationMs', 0) / 1000
                })
            print(f"Successfully fetched transcript with {len(transcript)} entries using httpx")
            return transcript
    try:
        # Always use asyncio.run() in Vercel environment
        return asyncio.run(_fetch())
    except Exception as e:
        print(f"Error fetching transcript with httpx: {e}")
        traceback.print_exc()
        raise Exception(f"Failed to fetch transcript with httpx: {str(e)}")


def test_xml_error_handling(video_id: str = "EngW7tLk6R8"):
    """
    Test function to verify XML error handling works correctly
    """
    print(f"Testing XML error handling for video: {video_id}")
    try:
        result = fetch_transcript(video_id, timeout_limit=60)
        if result:
            print(f"✅ Successfully fetched transcript with {len(result)} entries")
            return True
        else:
            print("❌ Failed to fetch transcript")
            return False
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False


if __name__ == "__main__":
    # Test the enhanced error handling
    test_video_ids = [
        "EngW7tLk6R8",  # Known working video
        "dQw4w9WgXcQ",  # Rick Roll - another test video
    ]

    for video_id in test_video_ids:
        print(f"\n{'='*50}")
        print(f"Testing video: {video_id}")
        print(f"{'='*50}")
        test_xml_error_handling(video_id)
