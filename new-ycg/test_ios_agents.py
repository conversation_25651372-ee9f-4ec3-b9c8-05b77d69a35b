#!/usr/bin/env python3
"""
Test script for iOS user agent prioritization
"""
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.utils.user_agents import (
    get_ios_user_agent_and_headers, 
    get_random_ios_user_agent_and_headers,
    get_next_user_agent_and_headers,
    UserAgentRotator
)


def test_ios_agent_prioritization():
    """Test iOS agent prioritization functionality"""
    print("📱 Testing iOS User Agent Prioritization")
    print("=" * 60)
    
    # Test iOS-specific functions
    print("\n🍎 Testing iOS-specific functions:")
    print("-" * 40)
    
    for i in range(5):
        user_agent, headers = get_ios_user_agent_and_headers()
        is_ios = "iPhone" in user_agent or "iPad" in user_agent
        device_type = "iPhone" if "iPhone" in user_agent else "iPad" if "iPad" in user_agent else "Unknown"
        
        print(f"iOS Agent {i+1}:")
        print(f"  Device: {device_type}")
        print(f"  User-Agent: {user_agent[:70]}...")
        print(f"  Is iOS: {'✅' if is_ios else '❌'}")
        print(f"  Headers count: {len(headers)}")
        print(f"  Has sec-fetch headers: {'sec-fetch-dest' in headers}")
        print()
    
    # Test random iOS agents
    print("\n🎲 Testing Random iOS agents:")
    print("-" * 40)
    
    ios_agents_seen = set()
    for i in range(3):
        user_agent, headers = get_random_ios_user_agent_and_headers()
        ios_agents_seen.add(user_agent)
        device_type = "iPhone" if "iPhone" in user_agent else "iPad" if "iPad" in user_agent else "Unknown"
        
        print(f"Random iOS {i+1}: {device_type} - {user_agent[:50]}...")
    
    print(f"\nUnique iOS agents in 3 attempts: {len(ios_agents_seen)}")
    
    # Test iOS vs regular rotation
    print("\n⚖️ Comparing iOS vs Regular rotation:")
    print("-" * 40)
    
    rotator = UserAgentRotator()
    
    print("iOS agents available:")
    ios_count = len([ua for ua in rotator.USER_AGENTS if "iPhone" in ua or "iPad" in ua])
    total_count = len(rotator.USER_AGENTS)
    print(f"  iOS agents: {ios_count}")
    print(f"  Total agents: {total_count}")
    print(f"  iOS percentage: {ios_count/total_count:.1%}")
    
    # Test that iOS agents are properly filtered
    print("\n🔍 Verifying iOS agent filtering:")
    for i in range(3):
        user_agent, headers = get_ios_user_agent_and_headers()
        is_actually_ios = "iPhone" in user_agent or "iPad" in user_agent
        print(f"  Agent {i+1}: {'✅ iOS' if is_actually_ios else '❌ Not iOS'}")
    
    print("\n✅ iOS Agent Prioritization Test Complete!")


def test_ios_headers():
    """Test iOS-specific headers"""
    print("\n📋 Testing iOS-specific headers:")
    print("-" * 40)
    
    rotator = UserAgentRotator()
    
    # Test iPhone headers
    iphone_agent = "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
    iphone_headers = rotator.get_browser_headers(iphone_agent)
    
    print("iPhone headers:")
    print(f"  Accept-Encoding: {iphone_headers.get('Accept-Encoding', 'N/A')}")
    print(f"  Has sec-fetch-dest: {'sec-fetch-dest' in iphone_headers}")
    print(f"  Accept: {iphone_headers.get('Accept', 'N/A')[:50]}...")
    
    # Test iPad headers
    ipad_agent = "Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
    ipad_headers = rotator.get_browser_headers(ipad_agent)
    
    print("\niPad headers:")
    print(f"  Accept-Encoding: {ipad_headers.get('Accept-Encoding', 'N/A')}")
    print(f"  Has sec-fetch-dest: {'sec-fetch-dest' in ipad_headers}")
    print(f"  Accept: {ipad_headers.get('Accept', 'N/A')[:50]}...")
    
    # Compare with desktop Chrome
    chrome_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    chrome_headers = rotator.get_browser_headers(chrome_agent)
    
    print("\nDesktop Chrome headers (for comparison):")
    print(f"  Accept-Encoding: {chrome_headers.get('Accept-Encoding', 'N/A')}")
    print(f"  Has sec-fetch-dest: {'sec-fetch-dest' in chrome_headers}")
    print(f"  Has sec-ch-ua: {'sec-ch-ua' in chrome_headers}")


if __name__ == "__main__":
    test_ios_agent_prioritization()
    test_ios_headers()
