{"version": 2, "builds": [{"src": "api/index.py", "use": "@vercel/python"}], "rewrites": [{"source": "/api/(.*)", "destination": "api/index.py"}, {"source": "/v1/(.*)", "destination": "api/index.py"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,POST,OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type,Accept"}]}], "public": true, "regions": ["iad1"]}