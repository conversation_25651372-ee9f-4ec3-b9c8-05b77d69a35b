#!/usr/bin/env python3
"""
Test script for user agent rotation functionality
"""
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.utils.user_agents import get_next_user_agent_and_headers, get_random_user_agent_and_headers, UserAgentRotator


def test_user_agent_rotation():
    """Test the user agent rotation functionality"""
    print("🧪 Testing User Agent Rotation")
    print("=" * 60)
    
    # Test sequential rotation
    print("\n📋 Testing Sequential Rotation:")
    print("-" * 40)
    
    for i in range(5):
        user_agent, headers = get_next_user_agent_and_headers()
        print(f"Attempt {i+1}:")
        print(f"  User-Agent: {user_agent[:80]}...")
        print(f"  Headers count: {len(headers)}")
        print(f"  Has sec-ch-ua: {'sec-ch-ua' in headers}")
        print(f"  Accept: {headers.get('Accept', 'N/A')[:50]}...")
        print()
    
    # Test random rotation
    print("\n🎲 Testing Random Rotation:")
    print("-" * 40)
    
    for i in range(3):
        user_agent, headers = get_random_user_agent_and_headers()
        print(f"Random {i+1}:")
        print(f"  User-Agent: {user_agent[:80]}...")
        print(f"  Platform: {headers.get('sec-ch-ua-platform', 'N/A')}")
        print()
    
    # Test browser detection
    print("\n🌐 Testing Browser-Specific Headers:")
    print("-" * 40)
    
    rotator = UserAgentRotator()
    
    # Test different browser types
    test_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
        "Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36"
    ]
    
    for agent in test_agents:
        headers = rotator.get_browser_headers(agent)
        browser_type = "Unknown"
        if "Chrome" in agent and "Edg" not in agent:
            browser_type = "Chrome"
        elif "Firefox" in agent:
            browser_type = "Firefox"
        elif "Safari" in agent and "Chrome" not in agent:
            browser_type = "Safari"
        elif "Edg" in agent:
            browser_type = "Edge"
        
        print(f"Browser: {browser_type}")
        print(f"  Mobile: {'Mobile' in agent}")
        print(f"  Has sec-ch-ua: {'sec-ch-ua' in headers}")
        print(f"  Platform: {headers.get('sec-ch-ua-platform', 'N/A')}")
        print()
    
    print("✅ User Agent Rotation Test Complete!")


def test_agent_diversity():
    """Test that we get diverse user agents"""
    print("\n🔄 Testing Agent Diversity:")
    print("-" * 40)
    
    rotator = UserAgentRotator()
    agents_seen = set()
    
    # Get 10 agents and check diversity
    for i in range(10):
        agent = rotator.get_next_agent()
        agents_seen.add(agent)
    
    print(f"Unique agents in 10 attempts: {len(agents_seen)}")
    print(f"Total available agents: {len(rotator.USER_AGENTS)}")
    print(f"Diversity ratio: {len(agents_seen)/10:.1%}")
    
    # Show some examples
    print("\nSample agents:")
    for i, agent in enumerate(list(agents_seen)[:3]):
        print(f"  {i+1}. {agent[:70]}...")


if __name__ == "__main__":
    test_user_agent_rotation()
    test_agent_diversity()
