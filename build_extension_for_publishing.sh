#!/bin/bash
# <PERSON><PERSON><PERSON> to build the YouTube Chapter Generator extension for publishing

echo "Building YouTube Chapter Generator extension for publishing..."

# Set the source and destination directories
SOURCE_DIR="../new-ycg-frontend/extension"
OUTPUT_DIR="dist"
ZIP_NAME="youtube_chapter_generator.zip"

# Check if source directory exists
if [ ! -d "$SOURCE_DIR" ]; then
  echo "Error: Source directory $SOURCE_DIR does not exist."
  exit 1
fi

# Create a clean dist directory
rm -rf "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR"

# Copy all extension files to the dist directory
echo "Copying extension files from $SOURCE_DIR to $OUTPUT_DIR..."
cp -r "$SOURCE_DIR"/* "$OUTPUT_DIR"/

# Create a zip file for publishing
echo "Creating zip file for publishing..."
cd "$OUTPUT_DIR" || exit
zip -r "../$ZIP_NAME" ./*
cd ..

echo "Extension built successfully."
echo "Zip file created at: $ZIP_NAME"
echo "You can now upload this zip file to the Chrome Web Store."
