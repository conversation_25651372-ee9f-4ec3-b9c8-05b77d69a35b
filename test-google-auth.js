/**
 * Test script for debugging Google authentication issues
 * 
 * This script tests the Google authentication flow in the YouTube Chapter Generator extension
 * to help diagnose the "Invalid refresh token" error.
 * 
 * To use:
 * 1. Open the extension popup
 * 2. Open Chrome DevTools (right-click > Inspect)
 * 3. Paste this script in the console and run it
 * 4. Check the console logs for detailed information about the Google auth process
 */

(async function() {
  console.log("=== GOOGLE AUTHENTICATION TEST SCRIPT ===");
  console.log("Starting Google authentication test...");

  // Check if we're in the extension context
  if (typeof chrome === 'undefined' || !chrome.identity) {
    console.error("This script must be run in the extension context with identity permission");
    return;
  }

  // Test 1: Check chrome.identity availability and permissions
  console.log("\n=== TEST 1: CHROME.IDENTITY AVAILABILITY ===");
  try {
    console.log("chrome.identity API available:", !!chrome.identity);
    console.log("chrome.identity methods:", {
      getAuthToken: typeof chrome.identity.getAuthToken === 'function',
      launchWebAuthFlow: typeof chrome.identity.launchWebAuthFlow === 'function',
      getProfileUserInfo: typeof chrome.identity.getProfileUserInfo === 'function',
      clearAllCachedAuthTokens: typeof chrome.identity.clearAllCachedAuthTokens === 'function'
    });
    
    // Check manifest permissions
    const manifest = chrome.runtime.getManifest();
    console.log("Extension manifest permissions:", manifest.permissions);
    console.log("Has identity permission:", manifest.permissions.includes('identity'));
    console.log("Has identity.email permission:", manifest.permissions.includes('identity.email'));
    
    // Check OAuth2 settings in manifest
    if (manifest.oauth2) {
      console.log("OAuth2 settings in manifest:", manifest.oauth2);
    } else {
      console.log("No OAuth2 settings found in manifest");
    }
  } catch (error) {
    console.error("Error checking chrome.identity availability:", error);
  }

  // Test 2: Try to get profile info (if available)
  console.log("\n=== TEST 2: GET PROFILE INFO ===");
  try {
    if (typeof chrome.identity.getProfileUserInfo === 'function') {
      const profileInfo = await new Promise((resolve) => {
        chrome.identity.getProfileUserInfo((info) => {
          resolve(info);
        });
      });
      
      console.log("Profile info available:", {
        hasEmail: !!profileInfo.email,
        email: profileInfo.email ? profileInfo.email : 'Not available',
        hasId: !!profileInfo.id,
        id: profileInfo.id ? profileInfo.id : 'Not available'
      });
    } else {
      console.log("getProfileUserInfo method not available");
    }
  } catch (error) {
    console.error("Error getting profile info:", error);
  }

  // Test 3: Try to get a Google token silently
  console.log("\n=== TEST 3: GET GOOGLE TOKEN SILENTLY ===");
  try {
    console.log("Attempting to get Google token silently...");
    
    // Clear cached tokens first
    await new Promise((resolve) => {
      chrome.identity.clearAllCachedAuthTokens(resolve);
    });
    console.log("Cleared cached Google tokens");
    
    // Get a new token without user interaction
    const token = await new Promise((resolve, reject) => {
      chrome.identity.getAuthToken({ interactive: false }, (token) => {
        if (chrome.runtime.lastError || !token) {
          const error = chrome.runtime.lastError ? 
            chrome.runtime.lastError.message : 
            "No token returned";
          
          reject(new Error(`Silent Google auth failed: ${error}`));
          return;
        }
        
        resolve(token);
      });
    });
    
    console.log("Successfully got Google token silently:", token.substring(0, 10) + "...");
    
    // Try to get token info from Google
    try {
      const response = await fetch(`https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${token}`);
      const tokenInfo = await response.json();
      
      console.log("Token info from Google:", {
        audience: tokenInfo.audience,
        scope: tokenInfo.scope,
        expiresIn: tokenInfo.expires_in + " seconds",
        email: tokenInfo.email,
        verifiedEmail: tokenInfo.verified_email
      });
    } catch (tokenInfoError) {
      console.error("Error getting token info from Google:", tokenInfoError);
    }
  } catch (error) {
    console.error("Error getting Google token silently:", error);
    
    // Try with interactive mode if silent mode fails
    console.log("\nTrying interactive mode as fallback...");
    try {
      const interactiveToken = await new Promise((resolve, reject) => {
        chrome.identity.getAuthToken({ interactive: true }, (token) => {
          if (chrome.runtime.lastError || !token) {
            const error = chrome.runtime.lastError ? 
              chrome.runtime.lastError.message : 
              "No token returned";
            
            reject(new Error(`Interactive Google auth failed: ${error}`));
            return;
          }
          
          resolve(token);
        });
      });
      
      console.log("Successfully got Google token interactively:", interactiveToken.substring(0, 10) + "...");
    } catch (interactiveError) {
      console.error("Error getting Google token interactively:", interactiveError);
    }
  }

  // Test 4: Check if the API's Google auth methods work
  console.log("\n=== TEST 4: API GOOGLE AUTH METHODS ===");
  if (window.YCG_API) {
    console.log("API service available, checking Google auth methods");
    
    // Check if the API has Google auth methods
    console.log("API Google auth methods available:", {
      hasGetGoogleTokenSilently: typeof window.YCG_API.getGoogleTokenSilently === 'function',
      hasLoginWithGoogle: typeof window.YCG_API.loginWithGoogle === 'function',
      hasRefreshViaGoogleSignIn: typeof window.YCG_API.refreshViaGoogleSignIn === 'function'
    });
    
    // Try the API's getGoogleTokenSilently method if available
    if (typeof window.YCG_API.getGoogleTokenSilently === 'function') {
      try {
        console.log("Calling API.getGoogleTokenSilently()...");
        const token = await window.YCG_API.getGoogleTokenSilently();
        console.log("Got token from API.getGoogleTokenSilently():", token ? `${token.substring(0, 10)}...` : 'No token');
      } catch (error) {
        console.error("Error calling API.getGoogleTokenSilently():", error);
      }
    }
    
    // Try the API's refreshViaGoogleSignIn method if available
    if (typeof window.YCG_API.refreshViaGoogleSignIn === 'function') {
      try {
        console.log("Calling API.refreshViaGoogleSignIn()...");
        const token = await window.YCG_API.refreshViaGoogleSignIn();
        console.log("Got token from API.refreshViaGoogleSignIn():", token ? `${token.substring(0, 10)}...` : 'No token');
      } catch (error) {
        console.error("Error calling API.refreshViaGoogleSignIn():", error);
      }
    }
  } else {
    console.log("API service not available");
  }

  console.log("\n=== GOOGLE AUTHENTICATION TEST COMPLETE ===");
})();
